{"swagger": "2.0", "info": {"description": "{ .Description }", "title": "{ .Title }", "contact": {"name": "CyberOwl", "url": "http://www.cyberowl.io", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "{ .Version }"}, "basePath": "{ .<PERSON><PERSON><PERSON> }", "paths": {"/assets": {"get": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["inventory"], "summary": "get all assets", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/modinv.Asset"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/assets/refreshAll": {"put": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["inventory"], "summary": "refresh all assets", "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "202": {"description": "Accepted", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/auth/verify": {"get": {"security": [{"ApiKeyAuth": []}], "description": "just verifies authentication", "produces": ["text/plain"], "tags": ["auth"], "summary": "verify", "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/hello": {"get": {"description": "just say hello, we're definitely here", "produces": ["application/json"], "tags": ["general"], "summary": "well hello there!", "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/locations": {"get": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["inventory"], "summary": "get all locations", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/modinv.Location"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/locations/refreshAll": {"put": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["inventory"], "summary": "refresh all locations", "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "202": {"description": "Accepted", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/processors": {"get": {"security": [{"ApiKeyAuth": []}], "description": "gets all processors", "produces": ["application/json"], "tags": ["processors"], "summary": "get all processors", "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/processors/start": {"post": {"security": [{"ApiKeyAuth": []}], "description": "starts processor", "produces": ["application/json"], "tags": ["processors"], "summary": "start processor", "parameters": [{"description": "the processor spec", "name": "spec", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-kafka-client_pkg_kcmodel.KafkaProcessorSpec"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/processors/stop/{name}": {"put": {"security": [{"ApiKeyAuth": []}], "description": "stops processor", "produces": ["application/json"], "tags": ["processors"], "summary": "stop processor", "parameters": [{"type": "string", "description": "Name of the processor", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/processors/stopAll": {"get": {"security": [{"ApiKeyAuth": []}], "description": "stop all processors", "produces": ["application/json"], "tags": ["processors"], "summary": "stop all processors", "responses": {"200": {"description": "OK", "schema": {"type": "json"}}}}}, "/users/refresh": {"get": {"security": [{"ApiKeyAuth": []}], "produces": ["application/json"], "tags": ["users"], "summary": "refresh all users", "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "202": {"description": "Accepted", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings": {"get": {"security": [{"ApiKeyAuth": []}], "description": "get a full array of all warnings", "produces": ["application/json"], "tags": ["warnings"], "summary": "get an array of all warnings", "parameters": [{"type": "boolean", "description": "If true, inactive warnings will be included. defaults to false", "name": "includeInactive", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "update a warning", "produces": ["application/json"], "tags": ["warnings"], "summary": "Update a warning", "parameters": [{"description": "the warning to update", "name": "warning", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.Warning"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "post": {"security": [{"ApiKeyAuth": []}], "description": "Create a warning", "produces": ["application/json"], "tags": ["warnings"], "summary": "Create a warning", "parameters": [{"description": "the warning to create", "name": "warning", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.Warning"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/config/warningsConfig": {"get": {"security": [{"ApiKeyAuth": []}], "description": "Will return the warning configs defined in the yaml", "produces": ["application/json"], "tags": ["warnings"], "summary": "Get all the current warning configs", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/warn.WarningConfigs"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/find": {"post": {"security": [{"ApiKeyAuth": []}], "description": "Find warnings", "produces": ["application/json"], "tags": ["warnings"], "summary": "Find warnings", "parameters": [{"description": "the warning to update", "name": "warning", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.WarningSearchCriteria"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/scores": {"post": {"security": [{"ApiKeyAuth": []}], "description": "Calculate the score of all warnings per category", "produces": ["application/json"], "tags": ["warnings"], "summary": "Calculate the score of all warnings per category", "parameters": [{"description": "the criteria of the warnings", "name": "code", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modcalc.Criteria"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/{identity}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "get a single warning from ID", "produces": ["application/json"], "tags": ["warnings"], "summary": "get a single warning", "parameters": [{"type": "string", "description": "Identity of the warning", "name": "identity", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "delete": {"security": [{"ApiKeyAuth": []}], "description": "Delete a warning from id", "produces": ["application/json"], "tags": ["warnings"], "summary": "Delete a warning", "parameters": [{"type": "string", "description": "Identity of the warning to delete", "name": "identity", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/{identity}/find": {"post": {"security": [{"ApiKeyAuth": []}], "description": "Find warnings", "produces": ["application/json"], "tags": ["warnings"], "summary": "Find warnings", "parameters": [{"type": "string", "description": "Identity of the warning", "name": "identity", "in": "path", "required": true}, {"description": "criteria to find warnings", "name": "warning", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.WarningSearchCriteria"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warnings/{identity}/state/{state}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "update warning state", "produces": ["application/json"], "tags": ["warnings"], "summary": "update warning state", "parameters": [{"type": "string", "description": "Identity of the warning", "name": "identity", "in": "path", "required": true}, {"type": "string", "description": "state", "name": "state", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warningsCode": {"post": {"security": [{"ApiKeyAuth": []}], "description": "get a single warning for code", "produces": ["application/json"], "tags": ["warnings"], "summary": "get a single warning for code", "parameters": [{"description": "code of the warning", "name": "code", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warningsHistory": {"get": {"security": [{"ApiKeyAuth": []}], "description": "get a full array of all warning sHistory", "produces": ["application/json"], "tags": ["warnings history"], "summary": "get an array of all warnings History", "parameters": [{"type": "boolean", "description": "If true, inactive warnings History will be included. defaults to false", "name": "includeInactive", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "put": {"security": [{"ApiKeyAuth": []}], "description": "update a warning history", "produces": ["application/json"], "tags": ["warnings history"], "summary": "Update a warning history", "parameters": [{"description": "the warnings history to update", "name": "warningsHistory", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.WarningHistory"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "post": {"security": [{"ApiKeyAuth": []}], "description": "Create a warning history", "produces": ["application/json"], "tags": ["warnings history"], "summary": "Create a warning history", "parameters": [{"description": "the warning history to create", "name": "warningsHistory", "in": "body", "required": true, "schema": {"$ref": "#/definitions/modwarn.WarningHistory"}}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warningsHistory/warningId/{warningId}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "get a full array of all warning sHistory", "produces": ["application/json"], "tags": ["warnings history"], "summary": "get an array of all warnings History", "parameters": [{"type": "boolean", "description": "If true, inactive warnings History will be included. defaults to false", "name": "includeInactive", "in": "query"}, {"type": "string", "description": "Identity of the warning history", "name": "warningId", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}, "/warningsHistory/{identity}": {"get": {"security": [{"ApiKeyAuth": []}], "description": "get a single warning History from ID", "produces": ["application/json"], "tags": ["warnings history"], "summary": "get a single warning History", "parameters": [{"type": "string", "description": "Identity of the warning history", "name": "identity", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "json"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}, "delete": {"security": [{"ApiKeyAuth": []}], "description": "Delete a warning history from id", "produces": ["application/json"], "tags": ["warnings history"], "summary": "Delete a warning history", "parameters": [{"type": "string", "description": "Identity of the warning history to delete", "name": "identity", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "boolean"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult"}}}}}}, "definitions": {"github_com_CyberOwlTeam_go-kafka-client_pkg_kcmodel.KafkaProcessorSpec": {"type": "object", "properties": {"category": {"type": "string"}, "generateConsumerGroupId": {"type": "boolean"}, "name": {"type": "string"}, "topicLookup": {"type": "string"}}}, "github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB": {"type": "object", "additionalProperties": true}, "github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult": {"type": "object", "properties": {"message": {"type": "string"}, "success": {"type": "boolean"}}}, "mod.SearchCriteriaPagination": {"type": "object", "properties": {"pageOffset": {"description": "pagination starts @ ZERO", "type": "integer"}, "pageSize": {"type": "integer"}}}, "modcalc.Criteria": {"type": "object", "properties": {"categories": {"type": "array", "items": {"type": "string"}}, "fromCreatedAt": {"type": "string"}, "fromLastSeen": {"type": "string"}, "fromRelativeCreatedAt": {"type": "string", "example": "-1m"}, "fromRelativeLastSeen": {"type": "string", "example": "-1m"}, "fromRelativeUpdatedAt": {"type": "string", "example": "-1m"}, "fromUpdatedAt": {"type": "string"}, "locations": {"type": "array", "items": {"type": "string"}}, "toCreatedAt": {"type": "string"}, "toLastSeen": {"type": "string"}, "toRelativeCreatedAt": {"type": "string", "example": "-60s"}, "toRelativeLastSeen": {"type": "string", "example": "-60s"}, "toRelativeUpdatedAt": {"type": "string", "example": "-60s"}, "toUpdatedAt": {"type": "string"}}}, "modinv.Asset": {"type": "object", "properties": {"identity": {"type": "string"}, "location": {"type": "string"}, "name": {"type": "string"}}}, "modinv.Location": {"type": "object", "properties": {"identity": {"type": "string"}, "name": {"type": "string"}}}, "modwarn.Warning": {"type": "object", "properties": {"code": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB"}, "createdAt": {"type": "string"}, "eventType": {"type": "string"}, "identity": {"type": "string"}, "lastSeen": {"type": "string"}, "location": {"type": "string"}, "number": {"description": "-> indicates this will ALWAYS be omitted from inserts and updates", "type": "string"}, "risk": {"type": "number"}, "source": {"type": "string"}, "state": {"$ref": "#/definitions/modwarn.WarningState"}, "title": {"type": "string"}, "type": {"$ref": "#/definitions/modwarn.WarningType"}, "updatedAt": {"type": "string"}, "warningHistory": {"type": "array", "items": {"$ref": "#/definitions/modwarn.WarningHistory"}}}}, "modwarn.WarningColumn": {"type": "string", "enum": ["code", "type", "state", "risk", "source", "title", "location", "lastSeen", "createdAt", "updatedAt"], "x-enum-varnames": ["WarningColumnCode", "WarningColumnType", "WarningColumnState", "WarningColumnRisk", "WarningColumnSource", "WarningColumnTitle", "WarningColumnLocation", "WarningColumnLastSeen", "WarningColumnCreatedAt", "WarningColumnUpdatedAt"]}, "modwarn.WarningHistory": {"type": "object", "properties": {"context": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB"}, "createdAt": {"type": "string"}, "identity": {"type": "string"}, "lastSeen": {"type": "string"}, "risk": {"type": "number"}, "state": {"$ref": "#/definitions/modwarn.WarningState"}, "warningId": {"type": "string"}}}, "modwarn.WarningSearchCriteria": {"type": "object", "properties": {"code": {"$ref": "#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB"}, "eventTypes": {"type": "array", "items": {"type": "string"}}, "fromCreatedAt": {"type": "string"}, "fromLastSeen": {"type": "string"}, "fromRelativeCreatedAt": {"type": "string", "example": "-1m"}, "fromRelativeLastSeen": {"type": "string", "example": "-1m"}, "fromRelativeUpdatedAt": {"type": "string", "example": "-1m"}, "fromUpdatedAt": {"type": "string"}, "locations": {"type": "array", "items": {"type": "string"}}, "orderBy": {"$ref": "#/definitions/modwarn.WarningSearchOrderBy"}, "pagination": {"$ref": "#/definitions/mod.SearchCriteriaPagination"}, "riskFrom": {"type": "number"}, "riskTo": {"type": "number"}, "sources": {"type": "array", "items": {"type": "string"}}, "states": {"type": "array", "items": {"$ref": "#/definitions/modwarn.WarningState"}}, "title": {"type": "string"}, "toCreatedAt": {"type": "string"}, "toLastSeen": {"type": "string"}, "toRelativeCreatedAt": {"type": "string", "example": "-60s"}, "toRelativeLastSeen": {"type": "string", "example": "-60s"}, "toRelativeUpdatedAt": {"type": "string", "example": "-60s"}, "toUpdatedAt": {"type": "string"}, "types": {"type": "array", "items": {"$ref": "#/definitions/modwarn.WarningType"}}}}, "modwarn.WarningSearchOrderBy": {"type": "object", "properties": {"column": {"$ref": "#/definitions/modwarn.WarningColumn"}, "isAscending": {"type": "boolean"}}}, "modwarn.WarningState": {"type": "string", "enum": ["active", "muted", "dormant", "dismissed", "expired"], "x-enum-varnames": ["WarningStateActive", "WarningStateMuted", "WarningStateDormant", "WarningStateDismissed", "WarningStateExpired"]}, "modwarn.WarningType": {"type": "string", "enum": ["openEnded", "closedLoop"], "x-enum-varnames": ["WarningTypeOpenEnded", "WarningTypeClosedLoop"]}, "time.Duration": {"type": "integer", "enum": [-9223372036854775808, 9223372036854775807, 1, 1000, 1000000, 1000000000, 60000000000, 3600000000000, -9223372036854775808, 9223372036854775807, 1, 1000, 1000000, 1000000000, 60000000000, 3600000000000], "x-enum-varnames": ["minDuration", "maxDuration", "Nanosecond", "Microsecond", "Millisecond", "Second", "Minute", "Hour", "minDuration", "maxDuration", "Nanosecond", "Microsecond", "Millisecond", "Second", "Minute", "Hour"]}, "warn.EventItem": {"type": "object", "properties": {"displayName": {"type": "string"}, "dormancyPeriod": {"$ref": "#/definitions/time.Duration"}, "maxScore": {"type": "number"}, "name": {"type": "string"}, "recommendation": {"type": "string"}}}, "warn.SourceConfig": {"type": "object", "properties": {"displayName": {"type": "string"}, "eventConfigs": {"type": "array", "items": {"$ref": "#/definitions/warn.EventItem"}}, "maxScore": {"type": "number"}}}, "warn.WarningConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/warn.SourceConfig"}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}