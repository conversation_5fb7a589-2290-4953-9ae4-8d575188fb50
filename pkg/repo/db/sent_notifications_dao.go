package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type sentNotificationsDao interface {
	GetAllSentNotifications(tx ...*gorm.DB) ([]modnotif.SentNotification, error)
	RetrieveSentNotification(id uuid.UUID, tx ...*gorm.DB) (*modnotif.SentNotification, error)
	CreateSentNotification(sentNotification modnotif.SentNotification, tx ...*gorm.DB) (*modnotif.SentNotification, error)
	UpdateSentNotification(sentNotification modnotif.SentNotification, tx ...*gorm.DB) (*modnotif.SentNotification, error)
	DeleteSentNotification(id uuid.UUID, tx ...*gorm.DB) (bool, error)
}

func (d dao) GetAllSentNotifications(tx ...*gorm.DB) ([]modnotif.SentNotification, error) {
	var sentNotifications []modnotif.SentNotification

	err := d.getDb(tx...).
		Model(&modnotif.SentNotification{}).
		Preload("SentNotificationWarnings").
		Find(&sentNotifications).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return sentNotifications, nil
}

func (d dao) RetrieveSentNotification(id uuid.UUID, tx ...*gorm.DB) (*modnotif.SentNotification, error) {
	var sentNotification modnotif.SentNotification

	err := d.getDb(tx...).
		Model(&modnotif.SentNotification{}).
		Preload("SentNotificationWarnings").
		Where("id = ?", id).
		First(&sentNotification).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &sentNotification, err
}

func (d dao) CreateSentNotification(sentNotification modnotif.SentNotification, tx ...*gorm.DB) (*modnotif.SentNotification, error) {
	err := d.getDb(tx...).
		Session(&gorm.Session{FullSaveAssociations: true}).
		Model(&sentNotification).
		Create(&sentNotification).
		Error
	if err != nil {
		return nil, err
	}

	return &sentNotification, nil
}

func (d dao) UpdateSentNotification(sentNotification modnotif.SentNotification, tx ...*gorm.DB) (*modnotif.SentNotification, error) {
	err := d.getDb(tx...).
		Model(&sentNotification).
		Omit("SentNotificationWarnings").
		Updates(&sentNotification).
		Error

	if err != nil {
		return nil, err
	}

	return &sentNotification, nil
}

func (d dao) DeleteSentNotification(id uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.SentNotification{}).
		Delete(&modnotif.SentNotification{}, id).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}
