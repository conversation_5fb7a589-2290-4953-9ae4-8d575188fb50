basePath: '{ .BasePath }'
definitions:
  github_com_CyberOwlTeam_go-kafka-client_pkg_kcmodel.KafkaProcessorSpec:
    properties:
      category:
        type: string
      generateConsumerGroupId:
        type: boolean
      name:
        type: string
      topicLookup:
        type: string
    type: object
  github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB:
    additionalProperties: true
    type: object
  github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  mod.SearchCriteriaPagination:
    properties:
      pageOffset:
        description: pagination starts @ ZERO
        type: integer
      pageSize:
        type: integer
    type: object
  modcalc.Criteria:
    properties:
      categories:
        items:
          type: string
        type: array
      fromCreatedAt:
        type: string
      fromLastSeen:
        type: string
      fromRelativeCreatedAt:
        example: -1m
        type: string
      fromRelativeLastSeen:
        example: -1m
        type: string
      fromRelativeUpdatedAt:
        example: -1m
        type: string
      fromUpdatedAt:
        type: string
      locations:
        items:
          type: string
        type: array
      toCreatedAt:
        type: string
      toLastSeen:
        type: string
      toRelativeCreatedAt:
        example: -60s
        type: string
      toRelativeLastSeen:
        example: -60s
        type: string
      toRelativeUpdatedAt:
        example: -60s
        type: string
      toUpdatedAt:
        type: string
    type: object
  modinv.Asset:
    properties:
      identity:
        type: string
      location:
        type: string
      name:
        type: string
    type: object
  modinv.Location:
    properties:
      identity:
        type: string
      name:
        type: string
    type: object
  modwarn.Warning:
    properties:
      code:
        $ref: '#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB'
      createdAt:
        type: string
      eventType:
        type: string
      identity:
        type: string
      lastSeen:
        type: string
      location:
        type: string
      number:
        description: -> indicates this will ALWAYS be omitted from inserts and updates
        type: string
      risk:
        type: number
      source:
        type: string
      state:
        $ref: '#/definitions/modwarn.WarningState'
      title:
        type: string
      type:
        $ref: '#/definitions/modwarn.WarningType'
      updatedAt:
        type: string
      warningHistory:
        items:
          $ref: '#/definitions/modwarn.WarningHistory'
        type: array
    type: object
  modwarn.WarningColumn:
    enum:
    - code
    - type
    - state
    - risk
    - source
    - title
    - location
    - lastSeen
    - createdAt
    - updatedAt
    type: string
    x-enum-varnames:
    - WarningColumnCode
    - WarningColumnType
    - WarningColumnState
    - WarningColumnRisk
    - WarningColumnSource
    - WarningColumnTitle
    - WarningColumnLocation
    - WarningColumnLastSeen
    - WarningColumnCreatedAt
    - WarningColumnUpdatedAt
  modwarn.WarningHistory:
    properties:
      context:
        $ref: '#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB'
      createdAt:
        type: string
      identity:
        type: string
      lastSeen:
        type: string
      risk:
        type: number
      state:
        $ref: '#/definitions/modwarn.WarningState'
      warningId:
        type: string
    type: object
  modwarn.WarningSearchCriteria:
    properties:
      code:
        $ref: '#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB'
      eventTypes:
        items:
          type: string
        type: array
      fromCreatedAt:
        type: string
      fromLastSeen:
        type: string
      fromRelativeCreatedAt:
        example: -1m
        type: string
      fromRelativeLastSeen:
        example: -1m
        type: string
      fromRelativeUpdatedAt:
        example: -1m
        type: string
      fromUpdatedAt:
        type: string
      locations:
        items:
          type: string
        type: array
      orderBy:
        $ref: '#/definitions/modwarn.WarningSearchOrderBy'
      pagination:
        $ref: '#/definitions/mod.SearchCriteriaPagination'
      riskFrom:
        type: number
      riskTo:
        type: number
      sources:
        items:
          type: string
        type: array
      states:
        items:
          $ref: '#/definitions/modwarn.WarningState'
        type: array
      title:
        type: string
      toCreatedAt:
        type: string
      toLastSeen:
        type: string
      toRelativeCreatedAt:
        example: -60s
        type: string
      toRelativeLastSeen:
        example: -60s
        type: string
      toRelativeUpdatedAt:
        example: -60s
        type: string
      toUpdatedAt:
        type: string
      types:
        items:
          $ref: '#/definitions/modwarn.WarningType'
        type: array
    type: object
  modwarn.WarningSearchOrderBy:
    properties:
      column:
        $ref: '#/definitions/modwarn.WarningColumn'
      isAscending:
        type: boolean
    type: object
  modwarn.WarningState:
    enum:
    - active
    - muted
    - dormant
    - dismissed
    - expired
    type: string
    x-enum-varnames:
    - WarningStateActive
    - WarningStateMuted
    - WarningStateDormant
    - WarningStateDismissed
    - WarningStateExpired
  modwarn.WarningType:
    enum:
    - openEnded
    - closedLoop
    type: string
    x-enum-varnames:
    - WarningTypeOpenEnded
    - WarningTypeClosedLoop
  time.Duration:
    enum:
    - -9223372036854775808
    - 9223372036854775807
    - 1
    - 1000
    - 1000000
    - 1000000000
    - 60000000000
    - 3600000000000
    - -9223372036854775808
    - 9223372036854775807
    - 1
    - 1000
    - 1000000
    - 1000000000
    - 60000000000
    - 3600000000000
    type: integer
    x-enum-varnames:
    - minDuration
    - maxDuration
    - Nanosecond
    - Microsecond
    - Millisecond
    - Second
    - Minute
    - Hour
    - minDuration
    - maxDuration
    - Nanosecond
    - Microsecond
    - Millisecond
    - Second
    - Minute
    - Hour
  warn.EventItem:
    properties:
      displayName:
        type: string
      dormancyPeriod:
        $ref: '#/definitions/time.Duration'
      maxScore:
        type: number
      name:
        type: string
      recommendation:
        type: string
    type: object
  warn.SourceConfig:
    properties:
      displayName:
        type: string
      eventConfigs:
        items:
          $ref: '#/definitions/warn.EventItem'
        type: array
      maxScore:
        type: number
    type: object
  warn.WarningConfigs:
    additionalProperties:
      $ref: '#/definitions/warn.SourceConfig'
    type: object
info:
  contact:
    email: <EMAIL>
    name: CyberOwl
    url: http://www.cyberowl.io
  description: '{ .Description }'
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: '{ .Title }'
  version: '{ .Version }'
paths:
  /assets:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/modinv.Asset'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get all assets
      tags:
      - inventory
  /assets/refreshAll:
    put:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: boolean
        "202":
          description: Accepted
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: refresh all assets
      tags:
      - inventory
  /auth/verify:
    get:
      description: just verifies authentication
      produces:
      - text/plain
      responses:
        "200":
          description: OK
          schema:
            type: json
      security:
      - ApiKeyAuth: []
      summary: verify
      tags:
      - auth
  /hello:
    get:
      description: just say hello, we're definitely here
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
      summary: well hello there!
      tags:
      - general
  /locations:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/modinv.Location'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get all locations
      tags:
      - inventory
  /locations/refreshAll:
    put:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: boolean
        "202":
          description: Accepted
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: refresh all locations
      tags:
      - inventory
  /processors:
    get:
      description: gets all processors
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
      security:
      - ApiKeyAuth: []
      summary: get all processors
      tags:
      - processors
  /processors/start:
    post:
      description: starts processor
      parameters:
      - description: the processor spec
        in: body
        name: spec
        required: true
        schema:
          $ref: '#/definitions/github_com_CyberOwlTeam_go-kafka-client_pkg_kcmodel.KafkaProcessorSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
      security:
      - ApiKeyAuth: []
      summary: start processor
      tags:
      - processors
  /processors/stop/{name}:
    put:
      description: stops processor
      parameters:
      - description: Name of the processor
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
      security:
      - ApiKeyAuth: []
      summary: stop processor
      tags:
      - processors
  /processors/stopAll:
    get:
      description: stop all processors
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
      security:
      - ApiKeyAuth: []
      summary: stop all processors
      tags:
      - processors
  /users/refresh:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: boolean
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: refresh all users
      tags:
      - users
  /warnings:
    get:
      description: get a full array of all warnings
      parameters:
      - description: If true, inactive warnings will be included. defaults to false
        in: query
        name: includeInactive
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get an array of all warnings
      tags:
      - warnings
    post:
      description: Create a warning
      parameters:
      - description: the warning to create
        in: body
        name: warning
        required: true
        schema:
          $ref: '#/definitions/modwarn.Warning'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Create a warning
      tags:
      - warnings
    put:
      description: update a warning
      parameters:
      - description: the warning to update
        in: body
        name: warning
        required: true
        schema:
          $ref: '#/definitions/modwarn.Warning'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Update a warning
      tags:
      - warnings
  /warnings/{identity}:
    delete:
      description: Delete a warning from id
      parameters:
      - description: Identity of the warning to delete
        in: path
        name: identity
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: boolean
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Delete a warning
      tags:
      - warnings
    get:
      description: get a single warning from ID
      parameters:
      - description: Identity of the warning
        in: path
        name: identity
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get a single warning
      tags:
      - warnings
  /warnings/{identity}/find:
    post:
      description: Find warnings
      parameters:
      - description: Identity of the warning
        in: path
        name: identity
        required: true
        type: string
      - description: criteria to find warnings
        in: body
        name: warning
        required: true
        schema:
          $ref: '#/definitions/modwarn.WarningSearchCriteria'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Find warnings
      tags:
      - warnings
  /warnings/{identity}/state/{state}:
    get:
      description: update warning state
      parameters:
      - description: Identity of the warning
        in: path
        name: identity
        required: true
        type: string
      - description: state
        in: path
        name: state
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: update warning state
      tags:
      - warnings
  /warnings/config/warningsConfig:
    get:
      description: Will return the warning configs defined in the yaml
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/warn.WarningConfigs'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Get all the current warning configs
      tags:
      - warnings
  /warnings/find:
    post:
      description: Find warnings
      parameters:
      - description: the warning to update
        in: body
        name: warning
        required: true
        schema:
          $ref: '#/definitions/modwarn.WarningSearchCriteria'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Find warnings
      tags:
      - warnings
  /warnings/scores:
    post:
      description: Calculate the score of all warnings per category
      parameters:
      - description: the criteria of the warnings
        in: body
        name: code
        required: true
        schema:
          $ref: '#/definitions/modcalc.Criteria'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Calculate the score of all warnings per category
      tags:
      - warnings
  /warningsCode:
    post:
      description: get a single warning for code
      parameters:
      - description: code of the warning
        in: body
        name: code
        required: true
        schema:
          $ref: '#/definitions/github_com_CyberOwlTeam_go-utilities_pkg_cutils_cjson.JSONB'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get a single warning for code
      tags:
      - warnings
  /warningsHistory:
    get:
      description: get a full array of all warning sHistory
      parameters:
      - description: If true, inactive warnings History will be included. defaults
          to false
        in: query
        name: includeInactive
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get an array of all warnings History
      tags:
      - warnings history
    post:
      description: Create a warning history
      parameters:
      - description: the warning history to create
        in: body
        name: warningsHistory
        required: true
        schema:
          $ref: '#/definitions/modwarn.WarningHistory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Create a warning history
      tags:
      - warnings history
    put:
      description: update a warning history
      parameters:
      - description: the warnings history to update
        in: body
        name: warningsHistory
        required: true
        schema:
          $ref: '#/definitions/modwarn.WarningHistory'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Update a warning history
      tags:
      - warnings history
  /warningsHistory/{identity}:
    delete:
      description: Delete a warning history from id
      parameters:
      - description: Identity of the warning history to delete
        in: path
        name: identity
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: boolean
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: Delete a warning history
      tags:
      - warnings history
    get:
      description: get a single warning History from ID
      parameters:
      - description: Identity of the warning history
        in: path
        name: identity
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get a single warning History
      tags:
      - warnings history
  /warningsHistory/warningId/{warningId}:
    get:
      description: get a full array of all warning sHistory
      parameters:
      - description: If true, inactive warnings History will be included. defaults
          to false
        in: query
        name: includeInactive
        type: boolean
      - description: Identity of the warning history
        in: path
        name: warningId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: json
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/github_com_CyberOwlTeam_go-web-api_pkg_wapimod.ApiResult'
      security:
      - ApiKeyAuth: []
      summary: get an array of all warnings History
      tags:
      - warnings history
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
