// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package warning

import (
	modwarn "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	mock "github.com/stretchr/testify/mock"
)

// MockHistoryWarningService is an autogenerated mock type for the WarningService type
type MockHistoryWarningService struct {
	mock.Mock
}

type MockHistoryWarningService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockHistoryWarningService) EXPECT() *MockHistoryWarningService_Expecter {
	return &MockHistoryWarningService_Expecter{mock: &_m.Mock}
}

// ProcessWarning provides a mock function with given fields: _a0
func (_m *MockHistoryWarningService) ProcessWarning(_a0 modwarn.WarningDefinition) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for ProcessWarning")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningDefinition) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockHistoryWarningService_ProcessWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessWarning'
type MockHistoryWarningService_ProcessWarning_Call struct {
	*mock.Call
}

// ProcessWarning is a helper method to define mock.On call
//   - _a0 modwarn.WarningDefinition
func (_e *MockHistoryWarningService_Expecter) ProcessWarning(_a0 interface{}) *MockHistoryWarningService_ProcessWarning_Call {
	return &MockHistoryWarningService_ProcessWarning_Call{Call: _e.mock.On("ProcessWarning", _a0)}
}

func (_c *MockHistoryWarningService_ProcessWarning_Call) Run(run func(_a0 modwarn.WarningDefinition)) *MockHistoryWarningService_ProcessWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningDefinition))
	})
	return _c
}

func (_c *MockHistoryWarningService_ProcessWarning_Call) Return(_a0 error) *MockHistoryWarningService_ProcessWarning_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockHistoryWarningService_ProcessWarning_Call) RunAndReturn(run func(modwarn.WarningDefinition) error) *MockHistoryWarningService_ProcessWarning_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockHistoryWarningService creates a new instance of MockHistoryWarningService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockHistoryWarningService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockHistoryWarningService {
	mock := &MockHistoryWarningService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
