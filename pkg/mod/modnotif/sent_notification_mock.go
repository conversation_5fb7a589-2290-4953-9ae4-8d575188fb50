//go:build !compile

package modnotif

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/ctutils"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"testing"
)

func MockSentNotification1(t testing.TB) *SentNotification {
	return &SentNotification{
		Identity: *ctutils.MustParseUuid(t, "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		UserId:   *ctutils.MustParseUuid(t, "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		SentAt:   csource.MockTime1,
	}
}

func MockSentNotification2(t testing.TB) *SentNotification {
	return &SentNotification{
		Identity: *ctutils.MustParseUuid(t, "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
		UserId:   *ctutils.MustParseUuid(t, "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
		SentAt:   csource.MockTime2,
	}
}

func AllMockSentNotifications(t testing.TB) []SentNotification {
	return []SentNotification{
		*MockSentNotification1(t),
		*MockSentNotification2(t),
	}
}
