package db

import (
	"fmt"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type warningsDao interface {
	GetAllWarnings(tx ...*gorm.DB) ([]modwarn.Warning, error)
	RetrieveWarning(identity uuid.UUID, tx ...*gorm.DB) (*modwarn.Warning, error)
	GetWarningForCode(code cjson.JSONB, tx ...*gorm.DB) (*modwarn.Warning, error)
	FindWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) ([]modwarn.Warning, error)
	FindWarningsOffset(criteria modwarn.WarningSearchCriteria, identity uuid.UUID, tx ...*gorm.DB) (int, error)
	CreateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error)
	UpdateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error)
	DeleteWarning(identity uuid.UUID, tx ...*gorm.DB) (bool, error)
	CountTotalNumberOfWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) (int, error)
	GetNextWarningNumberSequence(tx ...*gorm.DB) (string, error)
	GetCurrentWarningNumberSequence(tx ...*gorm.DB) (string, error)
	ResetWarningNumberSequence(tx ...*gorm.DB) error
}

func (d dao) GetAllWarnings(tx ...*gorm.DB) ([]modwarn.Warning, error) {
	var warnings []modwarn.Warning

	err := d.getDb(tx...).
		Model(&modwarn.Warning{}).
		Preload("WarningHistory").
		Find(&warnings).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return warnings, nil
}

func (d dao) ResetWarningNumberSequence(tx ...*gorm.DB) error {
	return d.getDb(tx...).
		Exec("ALTER SEQUENCE warning_number_sequence RESTART").
		Error
}

func (d dao) GetNextWarningNumberSequence(tx ...*gorm.DB) (string, error) {
	var count int64
	err := d.getDb(tx...).
		Raw("SELECT NEXTVAL('warning_number_sequence')").
		Scan(&count).
		Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return "W001", nil
	}
	return fmt.Sprintf("W%03d", count), err
}

func (d dao) GetCurrentWarningNumberSequence(tx ...*gorm.DB) (string, error) {
	var count int64
	err := d.getDb(tx...).
		Raw("SELECT CURRVAL('warning_number_sequence')").
		Scan(&count).
		Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return "W001", nil
	}
	return fmt.Sprintf("W%03d", count), err
}

func (d dao) RetrieveWarning(identity uuid.UUID, tx ...*gorm.DB) (*modwarn.Warning, error) {
	var warning modwarn.Warning

	err := d.getDb(tx...).
		Table("warnings").
		Preload("WarningHistory").
		Where("id = ?", identity).
		First(&warning).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &warning, err
}

func (d dao) FindWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) ([]modwarn.Warning, error) {
	var warnings []modwarn.Warning

	tsx := d.getDb(tx...).
		Model(&modwarn.Warning{}).
		Preload("WarningHistory")

	tsx = applySearchCriteria(tsx, criteria, d.timeSource.New())

	tsx = tsx.Order(clause.OrderByColumn{
		Column: clause.Column{Name: criteria.OrderBy.GetOrderByColumn()},
		Desc:   criteria.OrderBy.GetIsDescending(),
	})

	tsx = applySearchCriteriaPagination(tsx, criteria.Pagination)

	err := tsx.Find(&warnings).Error

	return warnings, err
}

func (d dao) FindWarningsOffset(criteria modwarn.WarningSearchCriteria, identity uuid.UUID, tx ...*gorm.DB) (int, error) {
	var offset int

	findWarningsQuery := d.getDb(tx...).
		Model(&modwarn.Warning{})

	findWarningsQuery = applySearchCriteria(findWarningsQuery, criteria, d.timeSource.New())

	findWarningRowNumbersQuery := d.getDb(tx...).Table("(?) AS warnings", findWarningsQuery).
		Select(fmt.Sprintf("warnings.id, row_number() OVER (ORDER BY %s %s) AS row_number", criteria.OrderBy.GetCleanOrderByColumn(),
			criteria.OrderBy.GetOrderByDirection()))

	err := d.getDb(tx...).
		Table("(?) AS warnings_rows", findWarningRowNumbersQuery).
		Select("warnings_rows.row_number").
		Where("warnings_rows.id = ?", identity).
		Find(&offset).Error

	if err != nil {
		return 0, err
	}

	return offset, nil
}

func (d dao) GetWarningForCode(code cjson.JSONB, tx ...*gorm.DB) (*modwarn.Warning, error) {
	var warning modwarn.Warning

	err := d.getDb(tx...).
		Model(&modwarn.Warning{}).
		Preload("WarningHistory", func(db *gorm.DB) *gorm.DB {
			return db.Order("last_seen DESC") // get newest warnings first
		}).
		Where("code = ?", code).
		First(&warning).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &warning, err
}

func (d dao) CreateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error) {
	err := d.getDb(tx...).
		Session(&gorm.Session{FullSaveAssociations: true}).
		Model(&warning).
		Create(&warning).
		Error

	if err != nil {
		return nil, err
	}

	// attach the sequence
	currentSq, err := d.GetCurrentWarningNumberSequence(tx...)
	if err != nil {
		return nil, err
	}

	warning.Number = currentSq
	return &warning, nil
}

func (d dao) UpdateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error) {
	err := d.getDb(tx...).
		Model(&warning).
		Omit("WarningHistory").
		Updates(&warning).
		Error

	if err != nil {
		return nil, err
	}

	return &warning, nil
}

func (d dao) DeleteWarning(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modwarn.Warning{}).
		Delete(&modwarn.Warning{}, identity).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}

func applySearchCriteriaPagination(tx *gorm.DB, pagination *mod.SearchCriteriaPagination) *gorm.DB {
	if pagination == nil {
		return tx
	}

	if pagination.PageSize != nil {
		tx = tx.Limit(*pagination.PageSize)
	}

	if pagination.PageOffset != nil {
		tx = tx.Offset(*pagination.PageOffset)
	}

	return tx
}

func applySearchCriteria(tx *gorm.DB, criteria modwarn.WarningSearchCriteria, now time.Time) *gorm.DB {

	if criteria.Locations != nil {
		tx = tx.Where("location_id IN (?)", criteria.Locations)
	}
	if criteria.Types != nil {
		tx = tx.Where("type IN (?)", criteria.Types)
	}
	if criteria.States != nil {
		tx = tx.Where("state IN (?)", criteria.States)
	}
	if criteria.EventTypes != nil {
		tx = tx.Where("event_type IN (?)", criteria.EventTypes)
	}
	if criteria.RiskFrom != nil {
		tx = tx.Where("risk > ?", *criteria.RiskFrom)
	}
	if criteria.RiskTo != nil {
		tx = tx.Where("risk < ?", *criteria.RiskTo)
	}
	if criteria.Sources != nil {
		tx = tx.Where("source IN(?)", criteria.Sources)
	}
	if criteria.Title != nil {
		tx = tx.Where("title = ?", *criteria.Title)
	}

	if criteria.FromLastSeen != nil {
		tx = tx.Where("last_seen > ?", *criteria.FromLastSeen)
	}
	if criteria.FromRelativeLastSeen != nil {
		tx = tx.Where("last_seen > ?", now.Add(time.Duration(*criteria.FromRelativeLastSeen)))
	}
	if criteria.ToLastSeen != nil {
		tx = tx.Where("last_seen < ?", *criteria.ToLastSeen)
	}
	if criteria.ToRelativeLastSeen != nil {
		tx = tx.Where("last_seen < ?", now.Add(time.Duration(*criteria.ToRelativeLastSeen)))
	}

	if criteria.FromCreatedAt != nil {
		tx = tx.Where("created_at > ?", *criteria.FromCreatedAt)
	}
	if criteria.FromRelativeCreatedAt != nil {
		tx = tx.Where("created_at > ?", now.Add(time.Duration(*criteria.FromRelativeCreatedAt)))
	}
	if criteria.ToCreatedAt != nil {
		tx = tx.Where("created_at < ?", *criteria.ToCreatedAt)
	}
	if criteria.ToRelativeCreatedAt != nil {
		tx = tx.Where("created_at < ?", now.Add(time.Duration(*criteria.ToRelativeCreatedAt)))
	}

	if criteria.FromUpdatedAt != nil {
		tx = tx.Where("updated_at > ?", *criteria.FromUpdatedAt)
	}
	if criteria.FromRelativeUpdatedAt != nil {
		tx = tx.Where("updated_at > ?", now.Add(time.Duration(*criteria.FromRelativeUpdatedAt)))
	}
	if criteria.ToUpdatedAt != nil {
		tx = tx.Where("updated_at < ?", *criteria.ToUpdatedAt)
	}
	if criteria.ToRelativeUpdatedAt != nil {
		tx = tx.Where("updated_at < ?", now.Add(time.Duration(*criteria.ToRelativeUpdatedAt)))
	}

	return tx
}

func (d dao) CountTotalNumberOfWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) (int, error) {
	var totalNumberOfItems int64
	err := d.getDb(tx...).Transaction(func(tx *gorm.DB) error {
		tx = tx.Table("warnings")

		tx = applySearchCriteria(tx, criteria, d.timeSource.New())

		return tx.Count(&totalNumberOfItems).Error
	})

	return int(totalNumberOfItems), err
}
