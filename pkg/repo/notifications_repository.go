package repo

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type NotificationsRepository interface {
	// warning notifications
	GetAllNotificationSettings() ([]modnotif.NotificationSetting, error)
	RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error)
	GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error)
	CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)
	UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)
	DeleteNotificationSetting(identity uuid.UUID) (bool, error)

	//warning notification rules
	GetAllNotificationRules() ([]modnotif.NotificationRule, error)
	RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error)
	CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error)
	UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error)
	DeleteNotificationRule(identity uuid.UUID) (bool, error)
	SwapNotificationRuleOrder(identity uuid.UUID, newOrder int) (*modnotif.NotificationRule, error)

	//warning affected items
	GetAllAffectedItems() ([]modnotif.AffectedItem, error)
	RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error)
	CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error)
	UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error)
	DeleteAffectedItem(identity uuid.UUID) (bool, error)

	// warnings users
	AssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error)
	UnAssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error)

	// notification queues
	GetAllNotificationQueues() ([]modnotif.NotificationQueue, error)
	RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error)
	GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error)
	CreateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)
	UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)
	DeleteNotificationQueue(id modnotif.NotificationQueueIdentity) (bool, error)
}

func (r *repository) GetAllNotificationSettings() ([]modnotif.NotificationSetting, error) {
	return r.dao.GetAllNotificationSettings()
}

func (r *repository) RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error) {
	return r.dao.RetrieveNotificationSetting(identity)
}

func (r *repository) GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error) {
	return r.dao.GetNotificationSettingByUserId(userId)
}

func (r *repository) CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	return r.dao.CreateNotificationSetting(setting)
}

func (r *repository) UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	return r.dao.UpdateNotificationSetting(setting)
}

func (r *repository) DeleteNotificationSetting(identity uuid.UUID) (bool, error) {
	var success bool
	err := r.dao.Transaction(func(tx *gorm.DB) error {
		var err error
		success, err = r.deleteNotificationSetting(identity, tx)
		return err
	})

	if err != nil {
		return false, err
	}

	return success, err
}

func (r *repository) GetAllNotificationRules() ([]modnotif.NotificationRule, error) {
	return r.dao.GetAllNotificationRules()
}

func (r *repository) RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error) {
	return r.dao.RetrieveNotificationRule(identity)
}

func (r *repository) CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	return r.dao.CreateNotificationRule(rule)
}

func (r *repository) UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	return r.dao.UpdateNotificationRule(rule)
}

func (r *repository) DeleteNotificationRule(identity uuid.UUID) (bool, error) {
	var success bool
	err := r.dao.Transaction(func(tx *gorm.DB) error {
		var err error
		success, err = r.deleteNotificationRule(identity, tx)
		return err
	})

	if err != nil {
		return false, err
	}

	return success, err
}

func (r *repository) GetAllAffectedItems() ([]modnotif.AffectedItem, error) {
	return r.dao.GetAllAffectedItems()
}

func (r *repository) RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error) {
	return r.dao.RetrieveAffectedItem(identity)
}

func (r *repository) CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	return r.dao.CreateAffectedItem(item)
}

func (r *repository) UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	return r.dao.UpdateAffectedItem(item)
}

func (r *repository) DeleteAffectedItem(identity uuid.UUID) (bool, error) {
	return r.dao.DeleteAffectedItem(identity)
}

func (r *repository) deleteNotificationRule(identity uuid.UUID, tx *gorm.DB) (bool, error) {
	// delete entry from affected items notifs rules
	_, err := r.dao.DeleteAffectedItem4NotificationRule(identity, tx)
	if err != nil {
		return false, err
	}

	// finally, delete it from the notification rules table
	_, err = r.dao.DeleteNotificationRule(identity, tx)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r *repository) deleteNotificationSetting(identity uuid.UUID, tx *gorm.DB) (bool, error) {
	// delete entry from notif rules
	_, err := r.dao.DeleteNotificationRule4NotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	// delete from notif settings user
	_, err = r.dao.DeleteNotificationSettingsUserForNotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	// finally, delete it from the settings table itself
	_, err = r.dao.DeleteNotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r *repository) AssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error) {
	notificationSettingsToUsers := mapUsersToSettings(userIds, notificationSettingId)
	return r.dao.AssignUsersToNotificationSetting(notificationSettingsToUsers)
}

func (r *repository) UnAssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error) {
	notificationSettingsToUsers := mapUsersToSettings(userIds, notificationSettingId)
	return r.dao.UnAssignUsersToNotificationSetting(notificationSettingsToUsers)
}

func (r *repository) SwapNotificationRuleOrder(identity uuid.UUID, newOrder int) (*modnotif.NotificationRule, error) {
	var notif *modnotif.NotificationRule
	err := r.dao.Transaction(func(tx *gorm.DB) error {
		var err error
		notif, err = r.swapOrder(identity, newOrder, tx)
		return err
	})

	if err != nil {
		return nil, err
	}

	return notif, nil
}

func (r *repository) swapOrder(notificationRuleIdentity uuid.UUID, newOrder int, tx *gorm.DB) (*modnotif.NotificationRule, error) {
	currentRule, err := r.dao.RetrieveNotificationRule(notificationRuleIdentity, tx)
	if err != nil {
		return nil, err
	}
	if currentRule == nil {
		return nil, errors.New("notification rule not found")
	}

	if currentRule.Order == newOrder {
		return currentRule, nil
	}

	// Get all rules from the same notification setting
	rules, err := r.dao.GetNotificationRulesBySettingId(currentRule.NotificationSettingId, tx)
	if err != nil {
		return nil, err
	}

	oldOrder := currentRule.Order

	// Update the orders of affected rules
	if oldOrder < newOrder {
		//rules between old and new order move up one position
		for i := range rules {
			if rules[i].Order > oldOrder && rules[i].Order <= newOrder {
				rules[i].Order--
				_, err = r.dao.UpdateNotificationRule(rules[i], tx)
				if err != nil {
					return nil, err
				}
			}
		}
	} else {
		//rules between new and old order move down one position
		for i := range rules {
			if rules[i].Order >= newOrder && rules[i].Order < oldOrder {
				rules[i].Order++
				_, err = r.dao.UpdateNotificationRule(rules[i], tx)
				if err != nil {
					return nil, err
				}
			}
		}
	}

	currentRule.Order = newOrder
	return r.dao.UpdateNotificationRule(*currentRule, tx)
}

func mapUsersToSettings(userIds []uuid.UUID, notificationSettingId uuid.UUID) []modnotif.NotificationSettingsToUser {
	return lo.Map(userIds, func(userId uuid.UUID, index int) modnotif.NotificationSettingsToUser {
		return modnotif.NotificationSettingsToUser{
			NotificationSettingId: notificationSettingId,
			UserId:                userId,
		}
	})
}

func (r *repository) GetAllNotificationQueues() ([]modnotif.NotificationQueue, error) {
	return r.dao.GetAllNotificationQueues()
}

func (r *repository) RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error) {
	return r.dao.RetrieveNotificationQueue(id)
}

func (r *repository) GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error) {
	return r.dao.GetAllNotificationQueuesForUserAndFrequency(user, frequency)
}

func (r *repository) CreateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	return r.dao.CreateNotificationQueue(notificationQueue)
}

func (r *repository) UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	return r.dao.UpdateNotificationQueue(notificationQueue)
}
func (r *repository) DeleteNotificationQueue(id modnotif.NotificationQueueIdentity) (bool, error) {
	return r.dao.DeleteNotificationQueue(id)
}
