CREATE TABLE sent_notifications
(
    id UUID      NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id         UUID      NOT NULL,
    sent_at         TIMESTAMP NOT NULL,

    CONSTRAINT fk_user_id2sent_notifications
        FOREI<PERSON>N KEY (user_id)
            REFERENCES users (id) MATCH FULL
);

CREATE TABLE sent_notification_warnings
(
    warning_id      UUID NOT NULL,
    notification_id UUID NOT NULL,

    PRIMARY KEY (warning_id, notification_id),

    CONSTRAINT fk_warning_id2sent_notification_warnings
        FOREIGN KEY (warning_id)
            REFERENCES warnings (id) MATCH FULL,

    CONSTRAINT fk_notification_id2sent_notification_warnings
        FOREIGN KEY (notification_id)
            REFERENCES sent_notifications (id) MATCH FULL
);
