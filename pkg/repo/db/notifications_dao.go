package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type NotificationsDao interface {
	// warning notifications
	GetAllNotificationSettings(tx ...*gorm.DB) ([]modnotif.NotificationSetting, error)
	RetrieveNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error)
	GetNotificationSettingByUserId(userId uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error)
	CreateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error)
	UpdateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error)
	DeleteNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (bool, error)

	//warning notification rules
	GetAllNotificationRules(tx ...*gorm.DB) ([]modnotif.NotificationRule, error)
	RetrieveNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationRule, error)
	CreateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error)
	UpdateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error)
	DeleteNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (bool, error)
	DeleteNotificationRule4NotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error)

	//warning affected items
	GetAllAffectedItems(tx ...*gorm.DB) ([]modnotif.AffectedItem, error)
	RetrieveAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.AffectedItem, error)
	CreateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error)
	UpdateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error)
	DeleteAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (bool, error)
	DeleteAffectedItem4NotificationRule(notifRuleIdentity uuid.UUID, tx ...*gorm.DB) (bool, error)
	GetNotificationRulesBySettingId(notificationSettingId uuid.UUID, tx ...*gorm.DB) ([]modnotif.NotificationRule, error)

	// warnings users
	DeleteNotificationSettingsUserForNotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error)
	AssignUsersToNotificationSetting(notificationSettingsToUsers []modnotif.NotificationSettingsToUser, tx ...*gorm.DB) (bool, error)
	UnAssignUsersToNotificationSetting(notificationSettingsToUsers []modnotif.NotificationSettingsToUser, tx ...*gorm.DB) (bool, error)
}

func (d dao) GetAllNotificationSettings(tx ...*gorm.DB) ([]modnotif.NotificationSetting, error) {
	var settings []modnotif.NotificationSetting
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSetting{}).
		Preload("NotificationRules").
		Preload("NotificationRules.AffectedItems").
		Find(&settings).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return settings, nil
}

func (d dao) RetrieveNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	var setting modnotif.NotificationSetting
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSetting{}).
		Preload("NotificationRules").
		Preload("NotificationRules.AffectedItems").
		Where("id = ?", identity).
		First(&setting).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &setting, nil
}

func (d dao) GetNotificationSettingByUserId(userId uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	var setting modnotif.NotificationSetting
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSetting{}).
		Preload("NotificationRules", func(db *gorm.DB) *gorm.DB {
			return db.Order(clause.OrderByColumn{Column: clause.Column{Name: "order"}, Desc: false})
		}).
		Preload("NotificationRules.AffectedItems").
		Joins("JOIN notification_settings2users ON notification_settings2users.notification_setting_id = notification_settings.id").
		Where("notification_settings2users.user_id = ?", userId).
		First(&setting).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &setting, nil
}

func (d dao) CreateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	err := d.getDb(tx...).
		Session(&gorm.Session{FullSaveAssociations: true}).
		Model(&setting).
		Create(&setting).
		Error

	if err != nil {
		return nil, err
	}

	return &setting, nil
}

func (d dao) UpdateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	err := d.getDb(tx...).
		Model(&setting).
		Omit("NotificationRules", "Users").
		Updates(&setting).
		Error

	if err != nil {
		return nil, err
	}

	return &setting, nil
}

func (d dao) DeleteNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSetting{}).
		Delete(&modnotif.NotificationSetting{}, identity).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}

func (d dao) GetAllNotificationRules(tx ...*gorm.DB) ([]modnotif.NotificationRule, error) {
	var rules []modnotif.NotificationRule
	err := d.getDb(tx...).
		Model(&modnotif.NotificationRule{}).
		Preload("AffectedItems").
		Find(&rules).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return rules, nil
}

func (d dao) RetrieveNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	var rule modnotif.NotificationRule
	err := d.getDb(tx...).
		Model(&modnotif.NotificationRule{}).
		Preload("AffectedItems").
		Where("id = ?", identity).
		First(&rule).
		Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return &rule, nil
}

func (d dao) CreateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	err := d.getDb(tx...).
		Session(&gorm.Session{FullSaveAssociations: true}).
		Model(&rule).
		Create(&rule).
		Error

	if err != nil {
		return nil, err
	}

	return &rule, nil
}

func (d dao) UpdateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	err := d.getDb(tx...).
		Model(&rule).
		Omit("AffectedItems").
		Updates(&rule).
		Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

func (d dao) DeleteNotificationRule4NotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationRule{}).
		Where("notification_setting_id = ?", notifSettingIdentity).
		Delete(&modnotif.NotificationRule{}).
		Error

	if err != nil {
		return false, err
	}
	return true, nil
}

func (d dao) DeleteNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationRule{}).
		Delete(&modnotif.NotificationRule{}, identity).
		Error

	if err != nil {
		return false, err
	}
	return true, err
}

func (d dao) GetAllAffectedItems(tx ...*gorm.DB) ([]modnotif.AffectedItem, error) {
	var items []modnotif.AffectedItem
	err := d.getDb(tx...).
		Model(&modnotif.AffectedItem{}).
		Find(&items).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return items, nil
}

func (d dao) RetrieveAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	var item modnotif.AffectedItem
	err := d.getDb(tx...).
		Model(&modnotif.AffectedItem{}).
		Where("id = ?", identity).
		First(&item).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &item, nil
}

func (d dao) CreateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	err := d.getDb(tx...).
		Session(&gorm.Session{FullSaveAssociations: true}).
		Model(&item).
		Create(&item).
		Error

	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (d dao) UpdateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	err := d.getDb(tx...).
		Model(&item).
		Updates(&item).
		Error

	if err != nil {
		return nil, err
	}

	return &item, nil
}

func (d dao) DeleteAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.AffectedItem{}).
		Delete(&modnotif.AffectedItem{}, identity).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}

func (d dao) DeleteAffectedItem4NotificationRule(notifRuleIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.AffectedItem{}).
		Where("notification_rule_id = ?", notifRuleIdentity).
		Delete(&modnotif.AffectedItem{}).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}

func (d dao) GetNotificationRulesBySettingId(notificationSettingId uuid.UUID, tx ...*gorm.DB) ([]modnotif.NotificationRule, error) {
	var rules []modnotif.NotificationRule
	err := d.getDb(tx...).
		Model(&modnotif.NotificationRule{}).
		Where("notification_setting_id = ?", notificationSettingId).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "order"}, Desc: false}).
		Find(&rules).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return rules, nil
}

func (d dao) DeleteNotificationSettingsUserForNotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSettingsToUser{}).
		Where("notification_setting_id = ?", notifSettingIdentity).
		Delete(&modnotif.NotificationSettingsToUser{}).
		Error

	if err != nil {
		return false, err
	}
	return true, nil
}

func (d dao) AssignUsersToNotificationSetting(notificationSettingsToUsers []modnotif.NotificationSettingsToUser, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSettingsToUser{}).
		Create(&notificationSettingsToUsers).
		Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d dao) UnAssignUsersToNotificationSetting(notificationSettingsToUsers []modnotif.NotificationSettingsToUser, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Model(&modnotif.NotificationSettingsToUser{}).
		Delete(&notificationSettingsToUsers).
		Error

	if err != nil {
		return false, err
	}
	return true, nil
}
