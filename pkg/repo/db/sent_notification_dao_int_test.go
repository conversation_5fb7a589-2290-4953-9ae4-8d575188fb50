package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func (s *DaoIntegrationTestSuite) TestIntAssetDao_GetAllAssets() {
	// given
	expected := modinv.AllMockAssets(s.T())
	s.SetupLocations()
	s.SetupAssets(expected...)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllAssets()

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntAssetDao_RetrieveAsset() {
	// given
	expected := modinv.MockAsset1(s.T())
	s.SetupLocations()
	s.SetupAssets(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.RetrieveAsset(expected.Identity)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntAssetDao_CreateAsset() {
	// given
	expected := modinv.MockAsset1(s.T())
	s.SetupLocations()

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.CreateAsset(*expected)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveAsset(expected.Identity)
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntAssetDao_UpdateAsset() {
	// given
	expected := modinv.MockAsset1(s.T())
	s.SetupLocations()
	s.SetupAssets(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.UpdateAsset(*expected)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveAsset(expected.Identity)
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntAssetDao_DeleteAsset() {
	// given
	expected := modinv.MockAsset1(s.T())
	s.SetupLocations()
	s.SetupAssets(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.DeleteAsset(expected.Identity)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Truef(s.T(), result, "asset was not deleted")

	asset, err := dao.RetrieveAsset(expected.Identity)
	require.NoError(s.T(), err)
	assert.Nil(s.T(), asset)
}
