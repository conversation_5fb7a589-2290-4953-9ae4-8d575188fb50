package db

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cdb"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/pkg/errors"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type Dao interface {
	transactionsDao
	locationsDao
	assetsDao
	warningsDao
	warningsHistoryDao
	usersDao
	NotificationsDao
	notificationQueuesDao
	sentNotificationsDao
}

type dao struct {
	gdb        *gorm.DB
	timeSource csource.TimeSource
}

func NewDao(dbConfig *cdb.DbConfig, gdbs ...*gorm.DB) (Dao, error) {
	var gdb *gorm.DB
	var err error

	dsn := dbConfig.BuildDsn()
	if len(gdbs) > 0 && gdbs[0] != nil {
		gdb = gdbs[0]
	} else {
		gdb, err = gorm.Open(
			postgres.Open(dsn),
			&gorm.Config{
				Logger: cdb.NewDaoLogger(),
			})
		if err != nil {
			return nil, errors.Wrap(err, "a serious error occurred connecting to the database")
		}
	}

	db, _ := gdb.DB()

	if dbConfig.MaxIdleConns != nil {
		db.SetMaxIdleConns(*dbConfig.MaxIdleConns)
	}
	if dbConfig.MaxOpenConns != nil {
		db.SetMaxOpenConns(*dbConfig.MaxOpenConns)
	}
	if dbConfig.ConnMaxLifetime != nil {
		db.SetConnMaxLifetime(*dbConfig.ConnMaxLifetime)
	}
	if dbConfig.ConnMaxIdleTime != nil {
		db.SetConnMaxIdleTime(*dbConfig.ConnMaxIdleTime)
	}
	return newDao(gdb), nil
}

func newDao(gbd *gorm.DB) Dao {
	ts := csource.NewTimeSource()

	return &dao{
		gdb:        gbd,
		timeSource: ts,
	}
}

func (d dao) getDb(tx ...*gorm.DB) *gorm.DB {
	gbd := d.gdb
	if len(tx) == 1 && tx[0] != nil {
		gbd = tx[0]
	}
	return gbd
}
