// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package repo

import (
	authmod "github.com/CyberOwlTeam/go-authentication/pkg/authmod"
	cjson "github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"

	mock "github.com/stretchr/testify/mock"

	modinv "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"

	modnotif "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"

	modusr "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"

	modwarn "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"

	time "time"

	uuid "github.com/google/uuid"
)

// MockRepository is an autogenerated mock type for the Repository type
type MockRepository struct {
	mock.Mock
}

type MockRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepository) EXPECT() *MockRepository_Expecter {
	return &MockRepository_Expecter{mock: &_m.Mock}
}

// AssignUsersToNotificationSetting provides a mock function with given fields: userIds, notificationSettingId
func (_m *MockRepository) AssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error) {
	ret := _m.Called(userIds, notificationSettingId)

	if len(ret) == 0 {
		panic("no return value specified for AssignUsersToNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func([]uuid.UUID, uuid.UUID) (bool, error)); ok {
		return rf(userIds, notificationSettingId)
	}
	if rf, ok := ret.Get(0).(func([]uuid.UUID, uuid.UUID) bool); ok {
		r0 = rf(userIds, notificationSettingId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func([]uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(userIds, notificationSettingId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_AssignUsersToNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssignUsersToNotificationSetting'
type MockRepository_AssignUsersToNotificationSetting_Call struct {
	*mock.Call
}

// AssignUsersToNotificationSetting is a helper method to define mock.On call
//   - userIds []uuid.UUID
//   - notificationSettingId uuid.UUID
func (_e *MockRepository_Expecter) AssignUsersToNotificationSetting(userIds interface{}, notificationSettingId interface{}) *MockRepository_AssignUsersToNotificationSetting_Call {
	return &MockRepository_AssignUsersToNotificationSetting_Call{Call: _e.mock.On("AssignUsersToNotificationSetting", userIds, notificationSettingId)}
}

func (_c *MockRepository_AssignUsersToNotificationSetting_Call) Run(run func(userIds []uuid.UUID, notificationSettingId uuid.UUID)) *MockRepository_AssignUsersToNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]uuid.UUID), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_AssignUsersToNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockRepository_AssignUsersToNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_AssignUsersToNotificationSetting_Call) RunAndReturn(run func([]uuid.UUID, uuid.UUID) (bool, error)) *MockRepository_AssignUsersToNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAffectedItem provides a mock function with given fields: item
func (_m *MockRepository) CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	ret := _m.Called(item)

	if len(ret) == 0 {
		panic("no return value specified for CreateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)); ok {
		return rf(item)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) *modnotif.AffectedItem); ok {
		r0 = rf(item)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem) error); ok {
		r1 = rf(item)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAffectedItem'
type MockRepository_CreateAffectedItem_Call struct {
	*mock.Call
}

// CreateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
func (_e *MockRepository_Expecter) CreateAffectedItem(item interface{}) *MockRepository_CreateAffectedItem_Call {
	return &MockRepository_CreateAffectedItem_Call{Call: _e.mock.On("CreateAffectedItem", item)}
}

func (_c *MockRepository_CreateAffectedItem_Call) Run(run func(item modnotif.AffectedItem)) *MockRepository_CreateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.AffectedItem))
	})
	return _c
}

func (_c *MockRepository_CreateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockRepository_CreateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)) *MockRepository_CreateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAsset provides a mock function with given fields: asset
func (_m *MockRepository) CreateAsset(asset modinv.Asset) (*modinv.Asset, error) {
	ret := _m.Called(asset)

	if len(ret) == 0 {
		panic("no return value specified for CreateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset) (*modinv.Asset, error)); ok {
		return rf(asset)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset) *modinv.Asset); ok {
		r0 = rf(asset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset) error); ok {
		r1 = rf(asset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAsset'
type MockRepository_CreateAsset_Call struct {
	*mock.Call
}

// CreateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
func (_e *MockRepository_Expecter) CreateAsset(asset interface{}) *MockRepository_CreateAsset_Call {
	return &MockRepository_CreateAsset_Call{Call: _e.mock.On("CreateAsset", asset)}
}

func (_c *MockRepository_CreateAsset_Call) Run(run func(asset modinv.Asset)) *MockRepository_CreateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Asset))
	})
	return _c
}

func (_c *MockRepository_CreateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockRepository_CreateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateAsset_Call) RunAndReturn(run func(modinv.Asset) (*modinv.Asset, error)) *MockRepository_CreateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// CreateLocation provides a mock function with given fields: location
func (_m *MockRepository) CreateLocation(location modinv.Location) (*modinv.Location, error) {
	ret := _m.Called(location)

	if len(ret) == 0 {
		panic("no return value specified for CreateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location) (*modinv.Location, error)); ok {
		return rf(location)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location) *modinv.Location); ok {
		r0 = rf(location)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location) error); ok {
		r1 = rf(location)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLocation'
type MockRepository_CreateLocation_Call struct {
	*mock.Call
}

// CreateLocation is a helper method to define mock.On call
//   - location modinv.Location
func (_e *MockRepository_Expecter) CreateLocation(location interface{}) *MockRepository_CreateLocation_Call {
	return &MockRepository_CreateLocation_Call{Call: _e.mock.On("CreateLocation", location)}
}

func (_c *MockRepository_CreateLocation_Call) Run(run func(location modinv.Location)) *MockRepository_CreateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Location))
	})
	return _c
}

func (_c *MockRepository_CreateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockRepository_CreateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateLocation_Call) RunAndReturn(run func(modinv.Location) (*modinv.Location, error)) *MockRepository_CreateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationQueue provides a mock function with given fields: notificationQueue
func (_m *MockRepository) CreateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	ret := _m.Called(notificationQueue)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)); ok {
		return rf(notificationQueue)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue) *modnotif.NotificationQueue); ok {
		r0 = rf(notificationQueue)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueue) error); ok {
		r1 = rf(notificationQueue)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationQueue'
type MockRepository_CreateNotificationQueue_Call struct {
	*mock.Call
}

// CreateNotificationQueue is a helper method to define mock.On call
//   - notificationQueue modnotif.NotificationQueue
func (_e *MockRepository_Expecter) CreateNotificationQueue(notificationQueue interface{}) *MockRepository_CreateNotificationQueue_Call {
	return &MockRepository_CreateNotificationQueue_Call{Call: _e.mock.On("CreateNotificationQueue", notificationQueue)}
}

func (_c *MockRepository_CreateNotificationQueue_Call) Run(run func(notificationQueue modnotif.NotificationQueue)) *MockRepository_CreateNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationQueue))
	})
	return _c
}

func (_c *MockRepository_CreateNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockRepository_CreateNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)) *MockRepository_CreateNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationRule provides a mock function with given fields: rule
func (_m *MockRepository) CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	ret := _m.Called(rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)); ok {
		return rf(rule)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) *modnotif.NotificationRule); ok {
		r0 = rf(rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule) error); ok {
		r1 = rf(rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationRule'
type MockRepository_CreateNotificationRule_Call struct {
	*mock.Call
}

// CreateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
func (_e *MockRepository_Expecter) CreateNotificationRule(rule interface{}) *MockRepository_CreateNotificationRule_Call {
	return &MockRepository_CreateNotificationRule_Call{Call: _e.mock.On("CreateNotificationRule", rule)}
}

func (_c *MockRepository_CreateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule)) *MockRepository_CreateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationRule))
	})
	return _c
}

func (_c *MockRepository_CreateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockRepository_CreateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)) *MockRepository_CreateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationSetting provides a mock function with given fields: setting
func (_m *MockRepository) CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(setting)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) *modnotif.NotificationSetting); ok {
		r0 = rf(setting)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting) error); ok {
		r1 = rf(setting)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationSetting'
type MockRepository_CreateNotificationSetting_Call struct {
	*mock.Call
}

// CreateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
func (_e *MockRepository_Expecter) CreateNotificationSetting(setting interface{}) *MockRepository_CreateNotificationSetting_Call {
	return &MockRepository_CreateNotificationSetting_Call{Call: _e.mock.On("CreateNotificationSetting", setting)}
}

func (_c *MockRepository_CreateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting)) *MockRepository_CreateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationSetting))
	})
	return _c
}

func (_c *MockRepository_CreateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockRepository_CreateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)) *MockRepository_CreateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function with given fields: user
func (_m *MockRepository) CreateUser(user modusr.User) (*modusr.User, error) {
	ret := _m.Called(user)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User) (*modusr.User, error)); ok {
		return rf(user)
	}
	if rf, ok := ret.Get(0).(func(modusr.User) *modusr.User); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockRepository_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - user modusr.User
func (_e *MockRepository_Expecter) CreateUser(user interface{}) *MockRepository_CreateUser_Call {
	return &MockRepository_CreateUser_Call{Call: _e.mock.On("CreateUser", user)}
}

func (_c *MockRepository_CreateUser_Call) Run(run func(user modusr.User)) *MockRepository_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modusr.User))
	})
	return _c
}

func (_c *MockRepository_CreateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockRepository_CreateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateUser_Call) RunAndReturn(run func(modusr.User) (*modusr.User, error)) *MockRepository_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarning provides a mock function with given fields: warning
func (_m *MockRepository) CreateWarning(warning modwarn.Warning) (*modwarn.Warning, error) {
	ret := _m.Called(warning)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning) (*modwarn.Warning, error)); ok {
		return rf(warning)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning) *modwarn.Warning); ok {
		r0 = rf(warning)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning) error); ok {
		r1 = rf(warning)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarning'
type MockRepository_CreateWarning_Call struct {
	*mock.Call
}

// CreateWarning is a helper method to define mock.On call
//   - warning modwarn.Warning
func (_e *MockRepository_Expecter) CreateWarning(warning interface{}) *MockRepository_CreateWarning_Call {
	return &MockRepository_CreateWarning_Call{Call: _e.mock.On("CreateWarning", warning)}
}

func (_c *MockRepository_CreateWarning_Call) Run(run func(warning modwarn.Warning)) *MockRepository_CreateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.Warning))
	})
	return _c
}

func (_c *MockRepository_CreateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_CreateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateWarning_Call) RunAndReturn(run func(modwarn.Warning) (*modwarn.Warning, error)) *MockRepository_CreateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockRepository) CreateWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_CreateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarningHistory'
type MockRepository_CreateWarningHistory_Call struct {
	*mock.Call
}

// CreateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockRepository_Expecter) CreateWarningHistory(warningHistory interface{}) *MockRepository_CreateWarningHistory_Call {
	return &MockRepository_CreateWarningHistory_Call{Call: _e.mock.On("CreateWarningHistory", warningHistory)}
}

func (_c *MockRepository_CreateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockRepository_CreateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockRepository_CreateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockRepository_CreateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_CreateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockRepository_CreateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAffectedItem provides a mock function with given fields: identity
func (_m *MockRepository) DeleteAffectedItem(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAffectedItem")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAffectedItem'
type MockRepository_DeleteAffectedItem_Call struct {
	*mock.Call
}

// DeleteAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteAffectedItem(identity interface{}) *MockRepository_DeleteAffectedItem_Call {
	return &MockRepository_DeleteAffectedItem_Call{Call: _e.mock.On("DeleteAffectedItem", identity)}
}

func (_c *MockRepository_DeleteAffectedItem_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteAffectedItem_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteAffectedItem_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAsset provides a mock function with given fields: identity
func (_m *MockRepository) DeleteAsset(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAsset")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAsset'
type MockRepository_DeleteAsset_Call struct {
	*mock.Call
}

// DeleteAsset is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteAsset(identity interface{}) *MockRepository_DeleteAsset_Call {
	return &MockRepository_DeleteAsset_Call{Call: _e.mock.On("DeleteAsset", identity)}
}

func (_c *MockRepository_DeleteAsset_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteAsset_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteAsset_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteAsset_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteLocation provides a mock function with given fields: identity
func (_m *MockRepository) DeleteLocation(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLocation")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteLocation'
type MockRepository_DeleteLocation_Call struct {
	*mock.Call
}

// DeleteLocation is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteLocation(identity interface{}) *MockRepository_DeleteLocation_Call {
	return &MockRepository_DeleteLocation_Call{Call: _e.mock.On("DeleteLocation", identity)}
}

func (_c *MockRepository_DeleteLocation_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteLocation_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteLocation_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteLocation_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationQueue provides a mock function with given fields: id
func (_m *MockRepository) DeleteNotificationQueue(id modnotif.NotificationQueueIdentity) (bool, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationQueue")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity) (bool, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity) bool); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueueIdentity) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationQueue'
type MockRepository_DeleteNotificationQueue_Call struct {
	*mock.Call
}

// DeleteNotificationQueue is a helper method to define mock.On call
//   - id modnotif.NotificationQueueIdentity
func (_e *MockRepository_Expecter) DeleteNotificationQueue(id interface{}) *MockRepository_DeleteNotificationQueue_Call {
	return &MockRepository_DeleteNotificationQueue_Call{Call: _e.mock.On("DeleteNotificationQueue", id)}
}

func (_c *MockRepository_DeleteNotificationQueue_Call) Run(run func(id modnotif.NotificationQueueIdentity)) *MockRepository_DeleteNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationQueueIdentity))
	})
	return _c
}

func (_c *MockRepository_DeleteNotificationQueue_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueueIdentity) (bool, error)) *MockRepository_DeleteNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationRule provides a mock function with given fields: identity
func (_m *MockRepository) DeleteNotificationRule(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationRule")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationRule'
type MockRepository_DeleteNotificationRule_Call struct {
	*mock.Call
}

// DeleteNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteNotificationRule(identity interface{}) *MockRepository_DeleteNotificationRule_Call {
	return &MockRepository_DeleteNotificationRule_Call{Call: _e.mock.On("DeleteNotificationRule", identity)}
}

func (_c *MockRepository_DeleteNotificationRule_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteNotificationRule_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteNotificationRule_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationSetting provides a mock function with given fields: identity
func (_m *MockRepository) DeleteNotificationSetting(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationSetting'
type MockRepository_DeleteNotificationSetting_Call struct {
	*mock.Call
}

// DeleteNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteNotificationSetting(identity interface{}) *MockRepository_DeleteNotificationSetting_Call {
	return &MockRepository_DeleteNotificationSetting_Call{Call: _e.mock.On("DeleteNotificationSetting", identity)}
}

func (_c *MockRepository_DeleteNotificationSetting_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteNotificationSetting_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: identity
func (_m *MockRepository) DeleteUser(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockRepository_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteUser(identity interface{}) *MockRepository_DeleteUser_Call {
	return &MockRepository_DeleteUser_Call{Call: _e.mock.On("DeleteUser", identity)}
}

func (_c *MockRepository_DeleteUser_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteUser_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteUser_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarning provides a mock function with given fields: identity
func (_m *MockRepository) DeleteWarning(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarning")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarning'
type MockRepository_DeleteWarning_Call struct {
	*mock.Call
}

// DeleteWarning is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteWarning(identity interface{}) *MockRepository_DeleteWarning_Call {
	return &MockRepository_DeleteWarning_Call{Call: _e.mock.On("DeleteWarning", identity)}
}

func (_c *MockRepository_DeleteWarning_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteWarning_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteWarning_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteWarning_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarningHistory provides a mock function with given fields: identity
func (_m *MockRepository) DeleteWarningHistory(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarningHistory")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_DeleteWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarningHistory'
type MockRepository_DeleteWarningHistory_Call struct {
	*mock.Call
}

// DeleteWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) DeleteWarningHistory(identity interface{}) *MockRepository_DeleteWarningHistory_Call {
	return &MockRepository_DeleteWarningHistory_Call{Call: _e.mock.On("DeleteWarningHistory", identity)}
}

func (_c *MockRepository_DeleteWarningHistory_Call) Run(run func(identity uuid.UUID)) *MockRepository_DeleteWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_DeleteWarningHistory_Call) Return(_a0 bool, _a1 error) *MockRepository_DeleteWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_DeleteWarningHistory_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockRepository_DeleteWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// FindWarnings provides a mock function with given fields: criteria, identity
func (_m *MockRepository) FindWarnings(criteria modwarn.WarningSearchCriteria, identity *uuid.UUID) (*modwarn.WarningSearchResult, error) {
	ret := _m.Called(criteria, identity)

	if len(ret) == 0 {
		panic("no return value specified for FindWarnings")
	}

	var r0 *modwarn.WarningSearchResult
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, *uuid.UUID) (*modwarn.WarningSearchResult, error)); ok {
		return rf(criteria, identity)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, *uuid.UUID) *modwarn.WarningSearchResult); ok {
		r0 = rf(criteria, identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningSearchResult)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningSearchCriteria, *uuid.UUID) error); ok {
		r1 = rf(criteria, identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_FindWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindWarnings'
type MockRepository_FindWarnings_Call struct {
	*mock.Call
}

// FindWarnings is a helper method to define mock.On call
//   - criteria modwarn.WarningSearchCriteria
//   - identity *uuid.UUID
func (_e *MockRepository_Expecter) FindWarnings(criteria interface{}, identity interface{}) *MockRepository_FindWarnings_Call {
	return &MockRepository_FindWarnings_Call{Call: _e.mock.On("FindWarnings", criteria, identity)}
}

func (_c *MockRepository_FindWarnings_Call) Run(run func(criteria modwarn.WarningSearchCriteria, identity *uuid.UUID)) *MockRepository_FindWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningSearchCriteria), args[1].(*uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_FindWarnings_Call) Return(_a0 *modwarn.WarningSearchResult, _a1 error) *MockRepository_FindWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_FindWarnings_Call) RunAndReturn(run func(modwarn.WarningSearchCriteria, *uuid.UUID) (*modwarn.WarningSearchResult, error)) *MockRepository_FindWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAffectedItems provides a mock function with no fields
func (_m *MockRepository) GetAllAffectedItems() ([]modnotif.AffectedItem, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAffectedItems")
	}

	var r0 []modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.AffectedItem, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.AffectedItem); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllAffectedItems_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAffectedItems'
type MockRepository_GetAllAffectedItems_Call struct {
	*mock.Call
}

// GetAllAffectedItems is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllAffectedItems() *MockRepository_GetAllAffectedItems_Call {
	return &MockRepository_GetAllAffectedItems_Call{Call: _e.mock.On("GetAllAffectedItems")}
}

func (_c *MockRepository_GetAllAffectedItems_Call) Run(run func()) *MockRepository_GetAllAffectedItems_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllAffectedItems_Call) Return(_a0 []modnotif.AffectedItem, _a1 error) *MockRepository_GetAllAffectedItems_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllAffectedItems_Call) RunAndReturn(run func() ([]modnotif.AffectedItem, error)) *MockRepository_GetAllAffectedItems_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAssets provides a mock function with no fields
func (_m *MockRepository) GetAllAssets() ([]modinv.Asset, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAssets")
	}

	var r0 []modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modinv.Asset, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modinv.Asset); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAssets'
type MockRepository_GetAllAssets_Call struct {
	*mock.Call
}

// GetAllAssets is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllAssets() *MockRepository_GetAllAssets_Call {
	return &MockRepository_GetAllAssets_Call{Call: _e.mock.On("GetAllAssets")}
}

func (_c *MockRepository_GetAllAssets_Call) Run(run func()) *MockRepository_GetAllAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllAssets_Call) Return(_a0 []modinv.Asset, _a1 error) *MockRepository_GetAllAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllAssets_Call) RunAndReturn(run func() ([]modinv.Asset, error)) *MockRepository_GetAllAssets_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllLocations provides a mock function with no fields
func (_m *MockRepository) GetAllLocations() ([]modinv.Location, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllLocations")
	}

	var r0 []modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modinv.Location, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modinv.Location); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllLocations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllLocations'
type MockRepository_GetAllLocations_Call struct {
	*mock.Call
}

// GetAllLocations is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllLocations() *MockRepository_GetAllLocations_Call {
	return &MockRepository_GetAllLocations_Call{Call: _e.mock.On("GetAllLocations")}
}

func (_c *MockRepository_GetAllLocations_Call) Run(run func()) *MockRepository_GetAllLocations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllLocations_Call) Return(_a0 []modinv.Location, _a1 error) *MockRepository_GetAllLocations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllLocations_Call) RunAndReturn(run func() ([]modinv.Location, error)) *MockRepository_GetAllLocations_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationQueues provides a mock function with no fields
func (_m *MockRepository) GetAllNotificationQueues() ([]modnotif.NotificationQueue, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationQueues")
	}

	var r0 []modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.NotificationQueue, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.NotificationQueue); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllNotificationQueues_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationQueues'
type MockRepository_GetAllNotificationQueues_Call struct {
	*mock.Call
}

// GetAllNotificationQueues is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllNotificationQueues() *MockRepository_GetAllNotificationQueues_Call {
	return &MockRepository_GetAllNotificationQueues_Call{Call: _e.mock.On("GetAllNotificationQueues")}
}

func (_c *MockRepository_GetAllNotificationQueues_Call) Run(run func()) *MockRepository_GetAllNotificationQueues_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllNotificationQueues_Call) Return(_a0 []modnotif.NotificationQueue, _a1 error) *MockRepository_GetAllNotificationQueues_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllNotificationQueues_Call) RunAndReturn(run func() ([]modnotif.NotificationQueue, error)) *MockRepository_GetAllNotificationQueues_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationQueuesForUserAndFrequency provides a mock function with given fields: user, frequency
func (_m *MockRepository) GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error) {
	ret := _m.Called(user, frequency)

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationQueuesForUserAndFrequency")
	}

	var r0 []modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error)); ok {
		return rf(user, frequency)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, modnotif.NotificationFrequency) []modnotif.NotificationQueue); ok {
		r0 = rf(user, frequency)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, modnotif.NotificationFrequency) error); ok {
		r1 = rf(user, frequency)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationQueuesForUserAndFrequency'
type MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call struct {
	*mock.Call
}

// GetAllNotificationQueuesForUserAndFrequency is a helper method to define mock.On call
//   - user uuid.UUID
//   - frequency modnotif.NotificationFrequency
func (_e *MockRepository_Expecter) GetAllNotificationQueuesForUserAndFrequency(user interface{}, frequency interface{}) *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call {
	return &MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call{Call: _e.mock.On("GetAllNotificationQueuesForUserAndFrequency", user, frequency)}
}

func (_c *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call) Run(run func(user uuid.UUID, frequency modnotif.NotificationFrequency)) *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(modnotif.NotificationFrequency))
	})
	return _c
}

func (_c *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call) Return(_a0 []modnotif.NotificationQueue, _a1 error) *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call) RunAndReturn(run func(uuid.UUID, modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error)) *MockRepository_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationRules provides a mock function with no fields
func (_m *MockRepository) GetAllNotificationRules() ([]modnotif.NotificationRule, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationRules")
	}

	var r0 []modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.NotificationRule, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.NotificationRule); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllNotificationRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationRules'
type MockRepository_GetAllNotificationRules_Call struct {
	*mock.Call
}

// GetAllNotificationRules is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllNotificationRules() *MockRepository_GetAllNotificationRules_Call {
	return &MockRepository_GetAllNotificationRules_Call{Call: _e.mock.On("GetAllNotificationRules")}
}

func (_c *MockRepository_GetAllNotificationRules_Call) Run(run func()) *MockRepository_GetAllNotificationRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllNotificationRules_Call) Return(_a0 []modnotif.NotificationRule, _a1 error) *MockRepository_GetAllNotificationRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllNotificationRules_Call) RunAndReturn(run func() ([]modnotif.NotificationRule, error)) *MockRepository_GetAllNotificationRules_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationSettings provides a mock function with no fields
func (_m *MockRepository) GetAllNotificationSettings() ([]modnotif.NotificationSetting, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationSettings")
	}

	var r0 []modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.NotificationSetting, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.NotificationSetting); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllNotificationSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationSettings'
type MockRepository_GetAllNotificationSettings_Call struct {
	*mock.Call
}

// GetAllNotificationSettings is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllNotificationSettings() *MockRepository_GetAllNotificationSettings_Call {
	return &MockRepository_GetAllNotificationSettings_Call{Call: _e.mock.On("GetAllNotificationSettings")}
}

func (_c *MockRepository_GetAllNotificationSettings_Call) Run(run func()) *MockRepository_GetAllNotificationSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllNotificationSettings_Call) Return(_a0 []modnotif.NotificationSetting, _a1 error) *MockRepository_GetAllNotificationSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllNotificationSettings_Call) RunAndReturn(run func() ([]modnotif.NotificationSetting, error)) *MockRepository_GetAllNotificationSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUsers provides a mock function with no fields
func (_m *MockRepository) GetAllUsers() ([]modusr.User, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllUsers")
	}

	var r0 []modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modusr.User, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modusr.User); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUsers'
type MockRepository_GetAllUsers_Call struct {
	*mock.Call
}

// GetAllUsers is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllUsers() *MockRepository_GetAllUsers_Call {
	return &MockRepository_GetAllUsers_Call{Call: _e.mock.On("GetAllUsers")}
}

func (_c *MockRepository_GetAllUsers_Call) Run(run func()) *MockRepository_GetAllUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllUsers_Call) Return(_a0 []modusr.User, _a1 error) *MockRepository_GetAllUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllUsers_Call) RunAndReturn(run func() ([]modusr.User, error)) *MockRepository_GetAllUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarnings provides a mock function with no fields
func (_m *MockRepository) GetAllWarnings() ([]modwarn.Warning, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarnings")
	}

	var r0 []modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modwarn.Warning, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modwarn.Warning); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarnings'
type MockRepository_GetAllWarnings_Call struct {
	*mock.Call
}

// GetAllWarnings is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllWarnings() *MockRepository_GetAllWarnings_Call {
	return &MockRepository_GetAllWarnings_Call{Call: _e.mock.On("GetAllWarnings")}
}

func (_c *MockRepository_GetAllWarnings_Call) Run(run func()) *MockRepository_GetAllWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllWarnings_Call) Return(_a0 []modwarn.Warning, _a1 error) *MockRepository_GetAllWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllWarnings_Call) RunAndReturn(run func() ([]modwarn.Warning, error)) *MockRepository_GetAllWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistory provides a mock function with no fields
func (_m *MockRepository) GetAllWarningsHistory() ([]modwarn.WarningHistory, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistory")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modwarn.WarningHistory, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modwarn.WarningHistory); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllWarningsHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistory'
type MockRepository_GetAllWarningsHistory_Call struct {
	*mock.Call
}

// GetAllWarningsHistory is a helper method to define mock.On call
func (_e *MockRepository_Expecter) GetAllWarningsHistory() *MockRepository_GetAllWarningsHistory_Call {
	return &MockRepository_GetAllWarningsHistory_Call{Call: _e.mock.On("GetAllWarningsHistory")}
}

func (_c *MockRepository_GetAllWarningsHistory_Call) Run(run func()) *MockRepository_GetAllWarningsHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRepository_GetAllWarningsHistory_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockRepository_GetAllWarningsHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllWarningsHistory_Call) RunAndReturn(run func() ([]modwarn.WarningHistory, error)) *MockRepository_GetAllWarningsHistory_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistoryForWarningId provides a mock function with given fields: warningId
func (_m *MockRepository) GetAllWarningsHistoryForWarningId(warningId uuid.UUID) ([]modwarn.WarningHistory, error) {
	ret := _m.Called(warningId)

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistoryForWarningId")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) ([]modwarn.WarningHistory, error)); ok {
		return rf(warningId)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) []modwarn.WarningHistory); ok {
		r0 = rf(warningId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(warningId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetAllWarningsHistoryForWarningId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistoryForWarningId'
type MockRepository_GetAllWarningsHistoryForWarningId_Call struct {
	*mock.Call
}

// GetAllWarningsHistoryForWarningId is a helper method to define mock.On call
//   - warningId uuid.UUID
func (_e *MockRepository_Expecter) GetAllWarningsHistoryForWarningId(warningId interface{}) *MockRepository_GetAllWarningsHistoryForWarningId_Call {
	return &MockRepository_GetAllWarningsHistoryForWarningId_Call{Call: _e.mock.On("GetAllWarningsHistoryForWarningId", warningId)}
}

func (_c *MockRepository_GetAllWarningsHistoryForWarningId_Call) Run(run func(warningId uuid.UUID)) *MockRepository_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetAllWarningsHistoryForWarningId_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockRepository_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetAllWarningsHistoryForWarningId_Call) RunAndReturn(run func(uuid.UUID) ([]modwarn.WarningHistory, error)) *MockRepository_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(run)
	return _c
}

// GetNotificationSettingByUserId provides a mock function with given fields: userId
func (_m *MockRepository) GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(userId)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationSettingByUserId")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationSetting, error)); ok {
		return rf(userId)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationSetting); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetNotificationSettingByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNotificationSettingByUserId'
type MockRepository_GetNotificationSettingByUserId_Call struct {
	*mock.Call
}

// GetNotificationSettingByUserId is a helper method to define mock.On call
//   - userId uuid.UUID
func (_e *MockRepository_Expecter) GetNotificationSettingByUserId(userId interface{}) *MockRepository_GetNotificationSettingByUserId_Call {
	return &MockRepository_GetNotificationSettingByUserId_Call{Call: _e.mock.On("GetNotificationSettingByUserId", userId)}
}

func (_c *MockRepository_GetNotificationSettingByUserId_Call) Run(run func(userId uuid.UUID)) *MockRepository_GetNotificationSettingByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_GetNotificationSettingByUserId_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockRepository_GetNotificationSettingByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetNotificationSettingByUserId_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationSetting, error)) *MockRepository_GetNotificationSettingByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// GetUser provides a mock function with given fields: email
func (_m *MockRepository) GetUser(email string) (authmod.IAuthUser, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetUser")
	}

	var r0 authmod.IAuthUser
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (authmod.IAuthUser, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) authmod.IAuthUser); ok {
		r0 = rf(email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(authmod.IAuthUser)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUser'
type MockRepository_GetUser_Call struct {
	*mock.Call
}

// GetUser is a helper method to define mock.On call
//   - email string
func (_e *MockRepository_Expecter) GetUser(email interface{}) *MockRepository_GetUser_Call {
	return &MockRepository_GetUser_Call{Call: _e.mock.On("GetUser", email)}
}

func (_c *MockRepository_GetUser_Call) Run(run func(email string)) *MockRepository_GetUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockRepository_GetUser_Call) Return(_a0 authmod.IAuthUser, _a1 error) *MockRepository_GetUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetUser_Call) RunAndReturn(run func(string) (authmod.IAuthUser, error)) *MockRepository_GetUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetWarningForCode provides a mock function with given fields: code
func (_m *MockRepository) GetWarningForCode(code cjson.JSONB) (*modwarn.Warning, error) {
	ret := _m.Called(code)

	if len(ret) == 0 {
		panic("no return value specified for GetWarningForCode")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(cjson.JSONB) (*modwarn.Warning, error)); ok {
		return rf(code)
	}
	if rf, ok := ret.Get(0).(func(cjson.JSONB) *modwarn.Warning); ok {
		r0 = rf(code)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(cjson.JSONB) error); ok {
		r1 = rf(code)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_GetWarningForCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWarningForCode'
type MockRepository_GetWarningForCode_Call struct {
	*mock.Call
}

// GetWarningForCode is a helper method to define mock.On call
//   - code cjson.JSONB
func (_e *MockRepository_Expecter) GetWarningForCode(code interface{}) *MockRepository_GetWarningForCode_Call {
	return &MockRepository_GetWarningForCode_Call{Call: _e.mock.On("GetWarningForCode", code)}
}

func (_c *MockRepository_GetWarningForCode_Call) Run(run func(code cjson.JSONB)) *MockRepository_GetWarningForCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(cjson.JSONB))
	})
	return _c
}

func (_c *MockRepository_GetWarningForCode_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_GetWarningForCode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_GetWarningForCode_Call) RunAndReturn(run func(cjson.JSONB) (*modwarn.Warning, error)) *MockRepository_GetWarningForCode_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAffectedItem provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.AffectedItem, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.AffectedItem); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAffectedItem'
type MockRepository_RetrieveAffectedItem_Call struct {
	*mock.Call
}

// RetrieveAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveAffectedItem(identity interface{}) *MockRepository_RetrieveAffectedItem_Call {
	return &MockRepository_RetrieveAffectedItem_Call{Call: _e.mock.On("RetrieveAffectedItem", identity)}
}

func (_c *MockRepository_RetrieveAffectedItem_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockRepository_RetrieveAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveAffectedItem_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.AffectedItem, error)) *MockRepository_RetrieveAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAsset provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveAsset(identity uuid.UUID) (*modinv.Asset, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modinv.Asset, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modinv.Asset); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAsset'
type MockRepository_RetrieveAsset_Call struct {
	*mock.Call
}

// RetrieveAsset is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveAsset(identity interface{}) *MockRepository_RetrieveAsset_Call {
	return &MockRepository_RetrieveAsset_Call{Call: _e.mock.On("RetrieveAsset", identity)}
}

func (_c *MockRepository_RetrieveAsset_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockRepository_RetrieveAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveAsset_Call) RunAndReturn(run func(uuid.UUID) (*modinv.Asset, error)) *MockRepository_RetrieveAsset_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveLocation provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveLocation(identity uuid.UUID) (*modinv.Location, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modinv.Location, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modinv.Location); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveLocation'
type MockRepository_RetrieveLocation_Call struct {
	*mock.Call
}

// RetrieveLocation is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveLocation(identity interface{}) *MockRepository_RetrieveLocation_Call {
	return &MockRepository_RetrieveLocation_Call{Call: _e.mock.On("RetrieveLocation", identity)}
}

func (_c *MockRepository_RetrieveLocation_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockRepository_RetrieveLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveLocation_Call) RunAndReturn(run func(uuid.UUID) (*modinv.Location, error)) *MockRepository_RetrieveLocation_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationQueue provides a mock function with given fields: id
func (_m *MockRepository) RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity) *modnotif.NotificationQueue); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueueIdentity) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationQueue'
type MockRepository_RetrieveNotificationQueue_Call struct {
	*mock.Call
}

// RetrieveNotificationQueue is a helper method to define mock.On call
//   - id modnotif.NotificationQueueIdentity
func (_e *MockRepository_Expecter) RetrieveNotificationQueue(id interface{}) *MockRepository_RetrieveNotificationQueue_Call {
	return &MockRepository_RetrieveNotificationQueue_Call{Call: _e.mock.On("RetrieveNotificationQueue", id)}
}

func (_c *MockRepository_RetrieveNotificationQueue_Call) Run(run func(id modnotif.NotificationQueueIdentity)) *MockRepository_RetrieveNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationQueueIdentity))
	})
	return _c
}

func (_c *MockRepository_RetrieveNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockRepository_RetrieveNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error)) *MockRepository_RetrieveNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationRule provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationRule, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationRule); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationRule'
type MockRepository_RetrieveNotificationRule_Call struct {
	*mock.Call
}

// RetrieveNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveNotificationRule(identity interface{}) *MockRepository_RetrieveNotificationRule_Call {
	return &MockRepository_RetrieveNotificationRule_Call{Call: _e.mock.On("RetrieveNotificationRule", identity)}
}

func (_c *MockRepository_RetrieveNotificationRule_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockRepository_RetrieveNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveNotificationRule_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationRule, error)) *MockRepository_RetrieveNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationSetting provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationSetting, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationSetting); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationSetting'
type MockRepository_RetrieveNotificationSetting_Call struct {
	*mock.Call
}

// RetrieveNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveNotificationSetting(identity interface{}) *MockRepository_RetrieveNotificationSetting_Call {
	return &MockRepository_RetrieveNotificationSetting_Call{Call: _e.mock.On("RetrieveNotificationSetting", identity)}
}

func (_c *MockRepository_RetrieveNotificationSetting_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockRepository_RetrieveNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveNotificationSetting_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationSetting, error)) *MockRepository_RetrieveNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveUser provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveUser(identity uuid.UUID) (*modusr.User, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modusr.User, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modusr.User); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveUser'
type MockRepository_RetrieveUser_Call struct {
	*mock.Call
}

// RetrieveUser is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveUser(identity interface{}) *MockRepository_RetrieveUser_Call {
	return &MockRepository_RetrieveUser_Call{Call: _e.mock.On("RetrieveUser", identity)}
}

func (_c *MockRepository_RetrieveUser_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveUser_Call) Return(_a0 *modusr.User, _a1 error) *MockRepository_RetrieveUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveUser_Call) RunAndReturn(run func(uuid.UUID) (*modusr.User, error)) *MockRepository_RetrieveUser_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarning provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveWarning(identity uuid.UUID) (*modwarn.Warning, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modwarn.Warning, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modwarn.Warning); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarning'
type MockRepository_RetrieveWarning_Call struct {
	*mock.Call
}

// RetrieveWarning is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveWarning(identity interface{}) *MockRepository_RetrieveWarning_Call {
	return &MockRepository_RetrieveWarning_Call{Call: _e.mock.On("RetrieveWarning", identity)}
}

func (_c *MockRepository_RetrieveWarning_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_RetrieveWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveWarning_Call) RunAndReturn(run func(uuid.UUID) (*modwarn.Warning, error)) *MockRepository_RetrieveWarning_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarningHistory provides a mock function with given fields: identity
func (_m *MockRepository) RetrieveWarningHistory(identity uuid.UUID) (*modwarn.WarningHistory, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modwarn.WarningHistory, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modwarn.WarningHistory); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_RetrieveWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarningHistory'
type MockRepository_RetrieveWarningHistory_Call struct {
	*mock.Call
}

// RetrieveWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockRepository_Expecter) RetrieveWarningHistory(identity interface{}) *MockRepository_RetrieveWarningHistory_Call {
	return &MockRepository_RetrieveWarningHistory_Call{Call: _e.mock.On("RetrieveWarningHistory", identity)}
}

func (_c *MockRepository_RetrieveWarningHistory_Call) Run(run func(identity uuid.UUID)) *MockRepository_RetrieveWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_RetrieveWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockRepository_RetrieveWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_RetrieveWarningHistory_Call) RunAndReturn(run func(uuid.UUID) (*modwarn.WarningHistory, error)) *MockRepository_RetrieveWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// SwapNotificationRuleOrder provides a mock function with given fields: identity, newOrder
func (_m *MockRepository) SwapNotificationRuleOrder(identity uuid.UUID, newOrder int) (*modnotif.NotificationRule, error) {
	ret := _m.Called(identity, newOrder)

	if len(ret) == 0 {
		panic("no return value specified for SwapNotificationRuleOrder")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, int) (*modnotif.NotificationRule, error)); ok {
		return rf(identity, newOrder)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, int) *modnotif.NotificationRule); ok {
		r0 = rf(identity, newOrder)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, int) error); ok {
		r1 = rf(identity, newOrder)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_SwapNotificationRuleOrder_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SwapNotificationRuleOrder'
type MockRepository_SwapNotificationRuleOrder_Call struct {
	*mock.Call
}

// SwapNotificationRuleOrder is a helper method to define mock.On call
//   - identity uuid.UUID
//   - newOrder int
func (_e *MockRepository_Expecter) SwapNotificationRuleOrder(identity interface{}, newOrder interface{}) *MockRepository_SwapNotificationRuleOrder_Call {
	return &MockRepository_SwapNotificationRuleOrder_Call{Call: _e.mock.On("SwapNotificationRuleOrder", identity, newOrder)}
}

func (_c *MockRepository_SwapNotificationRuleOrder_Call) Run(run func(identity uuid.UUID, newOrder int)) *MockRepository_SwapNotificationRuleOrder_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(int))
	})
	return _c
}

func (_c *MockRepository_SwapNotificationRuleOrder_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockRepository_SwapNotificationRuleOrder_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_SwapNotificationRuleOrder_Call) RunAndReturn(run func(uuid.UUID, int) (*modnotif.NotificationRule, error)) *MockRepository_SwapNotificationRuleOrder_Call {
	_c.Call.Return(run)
	return _c
}

// UnAssignUsersToNotificationSetting provides a mock function with given fields: userIds, notificationSettingId
func (_m *MockRepository) UnAssignUsersToNotificationSetting(userIds []uuid.UUID, notificationSettingId uuid.UUID) (bool, error) {
	ret := _m.Called(userIds, notificationSettingId)

	if len(ret) == 0 {
		panic("no return value specified for UnAssignUsersToNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func([]uuid.UUID, uuid.UUID) (bool, error)); ok {
		return rf(userIds, notificationSettingId)
	}
	if rf, ok := ret.Get(0).(func([]uuid.UUID, uuid.UUID) bool); ok {
		r0 = rf(userIds, notificationSettingId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func([]uuid.UUID, uuid.UUID) error); ok {
		r1 = rf(userIds, notificationSettingId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UnAssignUsersToNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnAssignUsersToNotificationSetting'
type MockRepository_UnAssignUsersToNotificationSetting_Call struct {
	*mock.Call
}

// UnAssignUsersToNotificationSetting is a helper method to define mock.On call
//   - userIds []uuid.UUID
//   - notificationSettingId uuid.UUID
func (_e *MockRepository_Expecter) UnAssignUsersToNotificationSetting(userIds interface{}, notificationSettingId interface{}) *MockRepository_UnAssignUsersToNotificationSetting_Call {
	return &MockRepository_UnAssignUsersToNotificationSetting_Call{Call: _e.mock.On("UnAssignUsersToNotificationSetting", userIds, notificationSettingId)}
}

func (_c *MockRepository_UnAssignUsersToNotificationSetting_Call) Run(run func(userIds []uuid.UUID, notificationSettingId uuid.UUID)) *MockRepository_UnAssignUsersToNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]uuid.UUID), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockRepository_UnAssignUsersToNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockRepository_UnAssignUsersToNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UnAssignUsersToNotificationSetting_Call) RunAndReturn(run func([]uuid.UUID, uuid.UUID) (bool, error)) *MockRepository_UnAssignUsersToNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAffectedItem provides a mock function with given fields: item
func (_m *MockRepository) UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	ret := _m.Called(item)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)); ok {
		return rf(item)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) *modnotif.AffectedItem); ok {
		r0 = rf(item)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem) error); ok {
		r1 = rf(item)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAffectedItem'
type MockRepository_UpdateAffectedItem_Call struct {
	*mock.Call
}

// UpdateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
func (_e *MockRepository_Expecter) UpdateAffectedItem(item interface{}) *MockRepository_UpdateAffectedItem_Call {
	return &MockRepository_UpdateAffectedItem_Call{Call: _e.mock.On("UpdateAffectedItem", item)}
}

func (_c *MockRepository_UpdateAffectedItem_Call) Run(run func(item modnotif.AffectedItem)) *MockRepository_UpdateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.AffectedItem))
	})
	return _c
}

func (_c *MockRepository_UpdateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockRepository_UpdateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)) *MockRepository_UpdateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAsset provides a mock function with given fields: asset
func (_m *MockRepository) UpdateAsset(asset modinv.Asset) (*modinv.Asset, error) {
	ret := _m.Called(asset)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset) (*modinv.Asset, error)); ok {
		return rf(asset)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset) *modinv.Asset); ok {
		r0 = rf(asset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset) error); ok {
		r1 = rf(asset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAsset'
type MockRepository_UpdateAsset_Call struct {
	*mock.Call
}

// UpdateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
func (_e *MockRepository_Expecter) UpdateAsset(asset interface{}) *MockRepository_UpdateAsset_Call {
	return &MockRepository_UpdateAsset_Call{Call: _e.mock.On("UpdateAsset", asset)}
}

func (_c *MockRepository_UpdateAsset_Call) Run(run func(asset modinv.Asset)) *MockRepository_UpdateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Asset))
	})
	return _c
}

func (_c *MockRepository_UpdateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockRepository_UpdateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateAsset_Call) RunAndReturn(run func(modinv.Asset) (*modinv.Asset, error)) *MockRepository_UpdateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLocation provides a mock function with given fields: location
func (_m *MockRepository) UpdateLocation(location modinv.Location) (*modinv.Location, error) {
	ret := _m.Called(location)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location) (*modinv.Location, error)); ok {
		return rf(location)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location) *modinv.Location); ok {
		r0 = rf(location)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location) error); ok {
		r1 = rf(location)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLocation'
type MockRepository_UpdateLocation_Call struct {
	*mock.Call
}

// UpdateLocation is a helper method to define mock.On call
//   - location modinv.Location
func (_e *MockRepository_Expecter) UpdateLocation(location interface{}) *MockRepository_UpdateLocation_Call {
	return &MockRepository_UpdateLocation_Call{Call: _e.mock.On("UpdateLocation", location)}
}

func (_c *MockRepository_UpdateLocation_Call) Run(run func(location modinv.Location)) *MockRepository_UpdateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Location))
	})
	return _c
}

func (_c *MockRepository_UpdateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockRepository_UpdateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateLocation_Call) RunAndReturn(run func(modinv.Location) (*modinv.Location, error)) *MockRepository_UpdateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationQueue provides a mock function with given fields: notificationQueue
func (_m *MockRepository) UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	ret := _m.Called(notificationQueue)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)); ok {
		return rf(notificationQueue)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue) *modnotif.NotificationQueue); ok {
		r0 = rf(notificationQueue)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueue) error); ok {
		r1 = rf(notificationQueue)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationQueue'
type MockRepository_UpdateNotificationQueue_Call struct {
	*mock.Call
}

// UpdateNotificationQueue is a helper method to define mock.On call
//   - notificationQueue modnotif.NotificationQueue
func (_e *MockRepository_Expecter) UpdateNotificationQueue(notificationQueue interface{}) *MockRepository_UpdateNotificationQueue_Call {
	return &MockRepository_UpdateNotificationQueue_Call{Call: _e.mock.On("UpdateNotificationQueue", notificationQueue)}
}

func (_c *MockRepository_UpdateNotificationQueue_Call) Run(run func(notificationQueue modnotif.NotificationQueue)) *MockRepository_UpdateNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationQueue))
	})
	return _c
}

func (_c *MockRepository_UpdateNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockRepository_UpdateNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)) *MockRepository_UpdateNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationRule provides a mock function with given fields: rule
func (_m *MockRepository) UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	ret := _m.Called(rule)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)); ok {
		return rf(rule)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) *modnotif.NotificationRule); ok {
		r0 = rf(rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule) error); ok {
		r1 = rf(rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationRule'
type MockRepository_UpdateNotificationRule_Call struct {
	*mock.Call
}

// UpdateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
func (_e *MockRepository_Expecter) UpdateNotificationRule(rule interface{}) *MockRepository_UpdateNotificationRule_Call {
	return &MockRepository_UpdateNotificationRule_Call{Call: _e.mock.On("UpdateNotificationRule", rule)}
}

func (_c *MockRepository_UpdateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule)) *MockRepository_UpdateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationRule))
	})
	return _c
}

func (_c *MockRepository_UpdateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockRepository_UpdateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)) *MockRepository_UpdateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationSetting provides a mock function with given fields: setting
func (_m *MockRepository) UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(setting)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) *modnotif.NotificationSetting); ok {
		r0 = rf(setting)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting) error); ok {
		r1 = rf(setting)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationSetting'
type MockRepository_UpdateNotificationSetting_Call struct {
	*mock.Call
}

// UpdateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
func (_e *MockRepository_Expecter) UpdateNotificationSetting(setting interface{}) *MockRepository_UpdateNotificationSetting_Call {
	return &MockRepository_UpdateNotificationSetting_Call{Call: _e.mock.On("UpdateNotificationSetting", setting)}
}

func (_c *MockRepository_UpdateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting)) *MockRepository_UpdateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationSetting))
	})
	return _c
}

func (_c *MockRepository_UpdateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockRepository_UpdateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)) *MockRepository_UpdateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function with given fields: user
func (_m *MockRepository) UpdateUser(user modusr.User) (*modusr.User, error) {
	ret := _m.Called(user)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User) (*modusr.User, error)); ok {
		return rf(user)
	}
	if rf, ok := ret.Get(0).(func(modusr.User) *modusr.User); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type MockRepository_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - user modusr.User
func (_e *MockRepository_Expecter) UpdateUser(user interface{}) *MockRepository_UpdateUser_Call {
	return &MockRepository_UpdateUser_Call{Call: _e.mock.On("UpdateUser", user)}
}

func (_c *MockRepository_UpdateUser_Call) Run(run func(user modusr.User)) *MockRepository_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modusr.User))
	})
	return _c
}

func (_c *MockRepository_UpdateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockRepository_UpdateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateUser_Call) RunAndReturn(run func(modusr.User) (*modusr.User, error)) *MockRepository_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarning provides a mock function with given fields: warning
func (_m *MockRepository) UpdateWarning(warning modwarn.Warning) (*modwarn.Warning, error) {
	ret := _m.Called(warning)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning) (*modwarn.Warning, error)); ok {
		return rf(warning)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning) *modwarn.Warning); ok {
		r0 = rf(warning)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning) error); ok {
		r1 = rf(warning)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarning'
type MockRepository_UpdateWarning_Call struct {
	*mock.Call
}

// UpdateWarning is a helper method to define mock.On call
//   - warning modwarn.Warning
func (_e *MockRepository_Expecter) UpdateWarning(warning interface{}) *MockRepository_UpdateWarning_Call {
	return &MockRepository_UpdateWarning_Call{Call: _e.mock.On("UpdateWarning", warning)}
}

func (_c *MockRepository_UpdateWarning_Call) Run(run func(warning modwarn.Warning)) *MockRepository_UpdateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.Warning))
	})
	return _c
}

func (_c *MockRepository_UpdateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_UpdateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateWarning_Call) RunAndReturn(run func(modwarn.Warning) (*modwarn.Warning, error)) *MockRepository_UpdateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockRepository) UpdateWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningHistory'
type MockRepository_UpdateWarningHistory_Call struct {
	*mock.Call
}

// UpdateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockRepository_Expecter) UpdateWarningHistory(warningHistory interface{}) *MockRepository_UpdateWarningHistory_Call {
	return &MockRepository_UpdateWarningHistory_Call{Call: _e.mock.On("UpdateWarningHistory", warningHistory)}
}

func (_c *MockRepository_UpdateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockRepository_UpdateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockRepository_UpdateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockRepository_UpdateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockRepository_UpdateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningLastSeenTimestamp provides a mock function with given fields: identity, timestamp
func (_m *MockRepository) UpdateWarningLastSeenTimestamp(identity uuid.UUID, timestamp time.Time) (*modwarn.Warning, error) {
	ret := _m.Called(identity, timestamp)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningLastSeenTimestamp")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, time.Time) (*modwarn.Warning, error)); ok {
		return rf(identity, timestamp)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, time.Time) *modwarn.Warning); ok {
		r0 = rf(identity, timestamp)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, time.Time) error); ok {
		r1 = rf(identity, timestamp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateWarningLastSeenTimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningLastSeenTimestamp'
type MockRepository_UpdateWarningLastSeenTimestamp_Call struct {
	*mock.Call
}

// UpdateWarningLastSeenTimestamp is a helper method to define mock.On call
//   - identity uuid.UUID
//   - timestamp time.Time
func (_e *MockRepository_Expecter) UpdateWarningLastSeenTimestamp(identity interface{}, timestamp interface{}) *MockRepository_UpdateWarningLastSeenTimestamp_Call {
	return &MockRepository_UpdateWarningLastSeenTimestamp_Call{Call: _e.mock.On("UpdateWarningLastSeenTimestamp", identity, timestamp)}
}

func (_c *MockRepository_UpdateWarningLastSeenTimestamp_Call) Run(run func(identity uuid.UUID, timestamp time.Time)) *MockRepository_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(time.Time))
	})
	return _c
}

func (_c *MockRepository_UpdateWarningLastSeenTimestamp_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateWarningLastSeenTimestamp_Call) RunAndReturn(run func(uuid.UUID, time.Time) (*modwarn.Warning, error)) *MockRepository_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningState provides a mock function with given fields: identity, newState
func (_m *MockRepository) UpdateWarningState(identity uuid.UUID, newState modwarn.WarningState) (*modwarn.Warning, error) {
	ret := _m.Called(identity, newState)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningState")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, modwarn.WarningState) (*modwarn.Warning, error)); ok {
		return rf(identity, newState)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, modwarn.WarningState) *modwarn.Warning); ok {
		r0 = rf(identity, newState)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, modwarn.WarningState) error); ok {
		r1 = rf(identity, newState)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpdateWarningState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningState'
type MockRepository_UpdateWarningState_Call struct {
	*mock.Call
}

// UpdateWarningState is a helper method to define mock.On call
//   - identity uuid.UUID
//   - newState modwarn.WarningState
func (_e *MockRepository_Expecter) UpdateWarningState(identity interface{}, newState interface{}) *MockRepository_UpdateWarningState_Call {
	return &MockRepository_UpdateWarningState_Call{Call: _e.mock.On("UpdateWarningState", identity, newState)}
}

func (_c *MockRepository_UpdateWarningState_Call) Run(run func(identity uuid.UUID, newState modwarn.WarningState)) *MockRepository_UpdateWarningState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(modwarn.WarningState))
	})
	return _c
}

func (_c *MockRepository_UpdateWarningState_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockRepository_UpdateWarningState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpdateWarningState_Call) RunAndReturn(run func(uuid.UUID, modwarn.WarningState) (*modwarn.Warning, error)) *MockRepository_UpdateWarningState_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockRepository) UpsertWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for UpsertWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UpsertWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertWarningHistory'
type MockRepository_UpsertWarningHistory_Call struct {
	*mock.Call
}

// UpsertWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockRepository_Expecter) UpsertWarningHistory(warningHistory interface{}) *MockRepository_UpsertWarningHistory_Call {
	return &MockRepository_UpsertWarningHistory_Call{Call: _e.mock.On("UpsertWarningHistory", warningHistory)}
}

func (_c *MockRepository_UpsertWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockRepository_UpsertWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockRepository_UpsertWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockRepository_UpsertWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UpsertWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockRepository_UpsertWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// UserHasRole provides a mock function with given fields: authUser, authUserRole
func (_m *MockRepository) UserHasRole(authUser authmod.IAuthUser, authUserRole []authmod.IAuthUserRole) (bool, error) {
	ret := _m.Called(authUser, authUserRole)

	if len(ret) == 0 {
		panic("no return value specified for UserHasRole")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(authmod.IAuthUser, []authmod.IAuthUserRole) (bool, error)); ok {
		return rf(authUser, authUserRole)
	}
	if rf, ok := ret.Get(0).(func(authmod.IAuthUser, []authmod.IAuthUserRole) bool); ok {
		r0 = rf(authUser, authUserRole)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(authmod.IAuthUser, []authmod.IAuthUserRole) error); ok {
		r1 = rf(authUser, authUserRole)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRepository_UserHasRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UserHasRole'
type MockRepository_UserHasRole_Call struct {
	*mock.Call
}

// UserHasRole is a helper method to define mock.On call
//   - authUser authmod.IAuthUser
//   - authUserRole []authmod.IAuthUserRole
func (_e *MockRepository_Expecter) UserHasRole(authUser interface{}, authUserRole interface{}) *MockRepository_UserHasRole_Call {
	return &MockRepository_UserHasRole_Call{Call: _e.mock.On("UserHasRole", authUser, authUserRole)}
}

func (_c *MockRepository_UserHasRole_Call) Run(run func(authUser authmod.IAuthUser, authUserRole []authmod.IAuthUserRole)) *MockRepository_UserHasRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(authmod.IAuthUser), args[1].([]authmod.IAuthUserRole))
	})
	return _c
}

func (_c *MockRepository_UserHasRole_Call) Return(_a0 bool, _a1 error) *MockRepository_UserHasRole_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRepository_UserHasRole_Call) RunAndReturn(run func(authmod.IAuthUser, []authmod.IAuthUserRole) (bool, error)) *MockRepository_UserHasRole_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRepository creates a new instance of MockRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepository {
	mock := &MockRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
