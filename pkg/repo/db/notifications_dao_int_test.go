package db

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/ctutils"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/stretchr/testify/require"
)

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_GetAllNotificationSettings() {
	// given
	expected := modnotif.AllMockNotificationSettings(s.T())
	s.SetupUsers()
	s.SetupNotificationSettings()

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllNotificationSettings()

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	removeUsers(expected)
	require.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_RetrieveNotificationSetting() {
	// given
	expected := modnotif.MockNotificationSetting1(s.T())
	s.SetupUsers()
	s.SetupNotificationSettings(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.RetrieveNotificationSetting(expected.Identity)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	removeUsers([]modnotif.NotificationSetting{*result})
	require.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_GetNotificationSettingByUserId() {
	// given
	expected := modnotif.MockNotificationSetting2(s.T())
	s.SetupUsers()
	s.SetupNotificationSettings(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetNotificationSettingByUserId(expected.Users[0].UserId)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	removeUser(expected)
	require.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_CreateNotificationSetting() {
	// given
	expected := modnotif.MockNotificationSetting2(s.T())
	s.SetupUsers()

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.CreateNotificationSetting(*expected)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_UpdateNotificationSetting() {
	// given
	expected := modnotif.MockNotificationSetting1(s.T())
	s.SetupNotificationSettings(*expected)

	updatedDescription := "updated description"
	expected.Description = updatedDescription

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.UpdateNotificationSetting(*expected)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), updatedDescription, result.Description)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_GetAllNotificationRules() {
	// given
	notifSettings := []modnotif.NotificationSetting{
		*modnotif.MockNotificationSetting3(s.T()),
		*modnotif.MockNotificationSetting4(s.T()),
	}
	expected := []modnotif.NotificationRule{
		*modnotif.MockNotificationRule3(s.T()),
		*modnotif.MockNotificationRule4(s.T()),
	}
	s.SetupUsers()
	s.SetupNotificationSettings(notifSettings...)
	s.SetupNotificationRules(expected...)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllNotificationRules()

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_RetrieveNotificationRule() {
	// given
	notifSetting := modnotif.MockNotificationSetting3(s.T())
	expected := modnotif.MockNotificationRule3(s.T())
	s.SetupUsers()
	s.SetupNotificationSettings(*notifSetting)
	s.SetupNotificationRules(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.RetrieveNotificationRule(expected.Identity)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_UpdateNotificationRule() {
	// given
	notifSetting := modnotif.MockNotificationSetting3(s.T())
	expected := modnotif.MockNotificationRule3(s.T())

	updatedSource := "updated source"
	expected.Source = updatedSource

	s.SetupUsers()
	s.SetupNotificationSettings(*notifSetting)
	s.SetupNotificationRules(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.UpdateNotificationRule(*expected)

	//then
	require.NoError(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), updatedSource, result.Source)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_DeleteNotificationRule4NotificationSetting() {
	// given
	notifSetting := modnotif.MockNotificationSetting3(s.T())
	expected := modnotif.MockNotificationRule3(s.T())
	s.SetupUsers()
	s.SetupNotificationSettings(*notifSetting)
	s.SetupNotificationRules(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.DeleteAffectedItem4NotificationRule(expected.Identity)
	require.NoError(s.T(), err)
	require.True(s.T(), result)

	result, err = dao.DeleteNotificationRule4NotificationSetting(notifSetting.Identity)

	//then
	require.NoError(s.T(), err)
	require.True(s.T(), result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_AssignUsersToNotificationSetting() {
	// given
	expected := []modnotif.NotificationSettingsToUser{
		{
			NotificationSettingId: csource.MockUuid3,
			UserId:                *ctutils.MustParseUuid(s.T(), "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		},
		{
			NotificationSettingId: csource.MockUuid3,
			UserId:                *ctutils.MustParseUuid(s.T(), "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
		},
	}
	notifSettings := []modnotif.NotificationSetting{
		*modnotif.MockNotificationSetting3(s.T()),
	}
	s.SetupUsers()
	s.SetupNotificationSettings(notifSettings...)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.AssignUsersToNotificationSetting(expected)

	//then
	require.NoError(s.T(), err)
	require.True(s.T(), result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationsDao_UnAssignUsersToNotificationSetting() {
	// given
	expected := []modnotif.NotificationSettingsToUser{
		{
			NotificationSettingId: csource.MockUuid3,
			UserId:                *ctutils.MustParseUuid(s.T(), "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		},
		{
			NotificationSettingId: csource.MockUuid3,
			UserId:                *ctutils.MustParseUuid(s.T(), "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
		},
	}
	notifSettings := []modnotif.NotificationSetting{
		*modnotif.MockNotificationSetting3(s.T()),
	}
	s.SetupUsers()
	s.SetupNotificationSettings(notifSettings...)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.AssignUsersToNotificationSetting(expected)
	require.NoError(s.T(), err)
	require.True(s.T(), result)

	result, err = dao.UnAssignUsersToNotificationSetting(expected)

	//then
	require.NoError(s.T(), err)
	require.True(s.T(), result)
}

func removeUsers(settings []modnotif.NotificationSetting) {
	for i := range settings {
		settings[i].Users = nil
	}
}

func removeUser(setting ...*modnotif.NotificationSetting) {
	for i := range setting {
		setting[i].Users = nil
	}
}
