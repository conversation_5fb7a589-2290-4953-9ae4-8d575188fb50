// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package inv

import (
	invmod "github.com/CyberOwlTeam/go-inventory-client/pkg/invmod"
	mock "github.com/stretchr/testify/mock"
)

// MockInvService is an autogenerated mock type for the InvService type
type MockInvService struct {
	mock.Mock
}

type MockInvService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockInvService) EXPECT() *MockInvService_Expecter {
	return &MockInvService_Expecter{mock: &_m.Mock}
}

// RefreshAllAssets provides a mock function with no fields
func (_m *MockInvService) RefreshAllAssets() ([]invmod.InventoryInconsistency, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefreshAllAssets")
	}

	var r0 []invmod.InventoryInconsistency
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]invmod.InventoryInconsistency, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []invmod.InventoryInconsistency); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]invmod.InventoryInconsistency)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvService_RefreshAllAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAllAssets'
type MockInvService_RefreshAllAssets_Call struct {
	*mock.Call
}

// RefreshAllAssets is a helper method to define mock.On call
func (_e *MockInvService_Expecter) RefreshAllAssets() *MockInvService_RefreshAllAssets_Call {
	return &MockInvService_RefreshAllAssets_Call{Call: _e.mock.On("RefreshAllAssets")}
}

func (_c *MockInvService_RefreshAllAssets_Call) Run(run func()) *MockInvService_RefreshAllAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockInvService_RefreshAllAssets_Call) Return(_a0 []invmod.InventoryInconsistency, _a1 error) *MockInvService_RefreshAllAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvService_RefreshAllAssets_Call) RunAndReturn(run func() ([]invmod.InventoryInconsistency, error)) *MockInvService_RefreshAllAssets_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshAllLocations provides a mock function with no fields
func (_m *MockInvService) RefreshAllLocations() ([]invmod.InventoryInconsistency, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefreshAllLocations")
	}

	var r0 []invmod.InventoryInconsistency
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]invmod.InventoryInconsistency, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []invmod.InventoryInconsistency); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]invmod.InventoryInconsistency)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockInvService_RefreshAllLocations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAllLocations'
type MockInvService_RefreshAllLocations_Call struct {
	*mock.Call
}

// RefreshAllLocations is a helper method to define mock.On call
func (_e *MockInvService_Expecter) RefreshAllLocations() *MockInvService_RefreshAllLocations_Call {
	return &MockInvService_RefreshAllLocations_Call{Call: _e.mock.On("RefreshAllLocations")}
}

func (_c *MockInvService_RefreshAllLocations_Call) Run(run func()) *MockInvService_RefreshAllLocations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockInvService_RefreshAllLocations_Call) Return(_a0 []invmod.InventoryInconsistency, _a1 error) *MockInvService_RefreshAllLocations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockInvService_RefreshAllLocations_Call) RunAndReturn(run func() ([]invmod.InventoryInconsistency, error)) *MockInvService_RefreshAllLocations_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockInvService creates a new instance of MockInvService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockInvService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockInvService {
	mock := &MockInvService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
