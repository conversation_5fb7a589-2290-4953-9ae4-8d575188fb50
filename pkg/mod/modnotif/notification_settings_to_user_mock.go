//go:build !compile

package modnotif

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/ctutils"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"testing"
)

func MockNotificationSettingsToUser1(t testing.TB) *NotificationSettingsToUser {
	return &NotificationSettingsToUser{
		NotificationSettingId:    csource.MockUuid2,
		UserId:                   *ctutils.MustParseUuid(t, "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		IsAdditionalEmailAddress: false,
		AdditionalEmailAddress:   "",
		GroupNotifications:       false,
	}
}

func MockNotificationSettingsToUser2(t testing.TB) *NotificationSettingsToUser {
	return &NotificationSettingsToUser{
		NotificationSettingId: csource.MockUuid1,
		UserId:                *ctutils.MustParseUuid(t, "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
	}
}

func AllMockNotificationSettingsToUsers(t testing.TB) []NotificationSettingsToUser {
	return []NotificationSettingsToUser{
		*MockNotificationSettingsToUser1(t),
	}
}
