package modnotif

import (
	"github.com/google/uuid"
	"time"
)

type SentNotification struct {
	Identity uuid.UUID                 `json:"identity" gorm:"column:id;primaryKey;default:uuid_generate_v4()"`
	UserId   uuid.UUID                 `json:"userId" gorm:"column:user_id"`
	SentAt   time.Time                 `json:"sentAt" gorm:"column:sent_at"`
	Warnings []SentNotificationWarning `json:"warnings" gorm:"foreignKey:notification_id;references:id"`
}

func (sn *SentNotification) TableName() string {
	return "sent_notifications"
}
