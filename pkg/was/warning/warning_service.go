package warning

import (
	"encoding/json"
	"fmt"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/samber/lo"
)

type WarningService interface {
	ProcessWarning(warning modwarn.WarningDefinition) error
}

type warningService struct {
	cfg        modcfg.MainConfig
	repo       repo.Repository
	timeSource csource.TimeSource
	uuidSource csource.UuidSource
}

func NewService(cfg modcfg.MainConfig, repo repo.Repository, timeSource csource.TimeSource, uuidSource csource.UuidSource) WarningService {
	return &warningService{
		cfg:        cfg,
		repo:       repo,
		timeSource: timeSource,
		uuidSource: uuidSource,
	}
}

func (s *warningService) ProcessWarning(warning modwarn.WarningDefinition) error {
	currentWarningForCode, err := s.repo.GetWarningForCode(warning.Code)
	if err != nil {
		return err
	}

	if warning.Type == modwarn.WarningTypeOpenEnded {
		err = s.processOpenEnded(warning, currentWarningForCode)
	} else {
		err = s.processClosedLoop(warning, currentWarningForCode)
	}

	if err != nil {
		return err
	}

	return nil
}

func (s *warningService) processClosedLoop(warningDef modwarn.WarningDefinition, existingWarning *modwarn.Warning) error {
	if warningDef.ClosedLoopState == nil {
		warningDef.ClosedLoopState = lo.ToPtr(modwarn.Open)
	}

	closedLoopState := *warningDef.ClosedLoopState

	if existingWarning != nil {
		currentWarningState := existingWarning.State

		// Determine the new state based on current state and closedLoopState
		var newState modwarn.WarningState
		switch currentWarningState {
		case modwarn.WarningStateActive:
			if closedLoopState == modwarn.Closed {
				newState = modwarn.WarningStateDormant
			}
		case modwarn.WarningStateDormant:
			if closedLoopState == modwarn.Open {
				newState = modwarn.WarningStateActive
			}
		}

		// Update the warning state if a new state is determined
		if newState != "" {
			updatedWarning, err := s.repo.UpdateWarningState(existingWarning.Identity, newState)
			if err != nil {
				return err
			}
			existingWarning = updatedWarning
		}

		// Handle context change
		_, _, err := s.contextChangeHandler(warningDef, existingWarning)
		if err != nil {
			return err
		}
	} else {
		stateToUse := closedLoopState.GetWarningState()

		// ignore any new warning where loop state is closed and the initial state is dormant
		if closedLoopState == modwarn.Closed && stateToUse == modwarn.WarningStateDormant {
			return nil
		}
		warning, _, err := s.createWarningAndHistoryEntry(warningDef, &stateToUse)
		if err != nil {
			return err
		}

		err = s.PopulateNotificationsQueue(*warning)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *warningService) processOpenEnded(warningDef modwarn.WarningDefinition, existingWarning *modwarn.Warning) error {
	if existingWarning != nil {
		// compare new context from received definition
		_, _, err := s.contextChangeHandler(warningDef, existingWarning)
		if err != nil {
			return err

		}
	} else {
		warning, _, err := s.createWarningAndHistoryEntry(warningDef, nil)
		if err != nil {
			return err
		}

		err = s.PopulateNotificationsQueue(*warning)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *warningService) contextChangeHandler(warningDef modwarn.WarningDefinition, existingWarning *modwarn.Warning) (warning *modwarn.Warning, warningHist *modwarn.WarningHistory, err error) {
	now := s.timeSource.New()
	if contextChanged(warningDef, existingWarning) {
		// if context is changed, add a new history entry
		warningHist, err = s.addHistoryEntry(existingWarning, warningDef.Context) // add new warning def with updated context
		if err != nil {
			return nil, nil, err
		}
	} else {
		if len(existingWarning.WarningHistory) > 0 {
			latestHistory := existingWarning.WarningHistory[0]
			latestHistory.LastSeen = now
			updatedHistory, err := s.repo.UpdateWarningHistory(latestHistory)
			if err != nil {
				return nil, nil, err
			}
			existingWarning.WarningHistory[0] = *updatedHistory
		}
	}

	// always update last seen timestamp
	updatedWarning, err := s.repo.UpdateWarningLastSeenTimestamp(existingWarning.Identity, now)
	if err != nil {
		return nil, nil, err
	}
	return updatedWarning, warningHist, nil
}

func (s *warningService) createWarningAndHistoryEntry(warningDef modwarn.WarningDefinition, stateToUse *modwarn.WarningState) (*modwarn.Warning, *modwarn.WarningHistory, error) {
	newWarning, err := s.repo.CreateWarning(modwarn.NewWarningFromDefinition(warningDef, stateToUse))
	if err != nil {
		return nil, nil, err
	}
	newHistory, err := s.addHistoryEntry(newWarning, warningDef.Context)
	if err != nil {
		return nil, nil, err
	}
	return newWarning, newHistory, nil
}

func (s *warningService) addHistoryEntry(w *modwarn.Warning, context cjson.JSONB) (*modwarn.WarningHistory, error) {
	hist := modwarn.WarningHistory{
		Identity:  s.uuidSource.NewUuid(),
		WarningId: w.Identity,
		Context:   context,
		State:     w.State,
		Risk:      w.Risk,
		LastSeen:  w.LastSeen,
		CreatedAt: s.timeSource.New(),
	}
	return s.repo.CreateWarningHistory(hist)
}

func getLatestContext(warning *modwarn.Warning) *cjson.JSONB {
	warningHistory := warning.WarningHistory
	if len(warningHistory) == 0 {
		return nil
	}

	return &warningHistory[0].Context
}

func contextChanged(warningDef modwarn.WarningDefinition, existingWarning *modwarn.Warning) bool {
	latestContext := getLatestContext(existingWarning)

	// If both are nil or empty
	if len(warningDef.Context) == 0 && latestContext == nil {
		return false
	}

	// If one is nil and the other isn't
	if (len(warningDef.Context) == 0 && latestContext != nil) || (len(warningDef.Context) > 0 && latestContext == nil) {
		return true
	}

	// Compare JSON content
	defBytes, _ := json.Marshal(warningDef.Context)
	latestBytes, _ := json.Marshal(*latestContext)
	return string(defBytes) != string(latestBytes)
}

func (s *warningService) PopulateNotificationsQueue(warning modwarn.Warning) error {
	users, err := s.repo.GetAllUsers()
	if err != nil {
		return err
	}

	for _, user := range users {
		rule, err := s.matchNotificationRule(warning, user)
		if err != nil {
			return err
		}

		if rule != nil {
			queue := modnotif.NotificationQueue{
				UserId:             user.Identity,
				NotificationRuleId: rule.Identity,
				WarningId:          warning.Identity,
				Frequency:          rule.Frequency,
			}

			_, err = s.repo.CreateNotificationQueue(queue)
			if err != nil {
				return err
			}
		}
	}

	return err
}

func (s *warningService) matchNotificationRule(warning modwarn.Warning, user modusr.User) (*modnotif.NotificationRule, error) {
	notificationSetting, err := s.repo.GetNotificationSettingByUserId(user.Identity)
	if err != nil {
		return nil, err
	}

	if notificationSetting == nil || len(notificationSetting.NotificationRules) == 0 {
		return nil, nil
	}

	for _, rule := range notificationSetting.NotificationRules {
		if rule.Source != warning.Source {
			continue
		}

		if rule.EventType != warning.EventType {
			continue
		}

		if !s.matchRiskToPriority(warning.Risk, rule.Priority) {
			continue
		}

		matchedAffectedItems, err := s.matchAffectedItems(rule.AffectedItems, warning)
		if err != nil {
			return nil, err
		}

		if !matchedAffectedItems {
			continue
		}

		return &rule, nil
	}

	return nil, nil
}

func (s *warningService) matchAffectedItems(affectedItems []modnotif.AffectedItem, warning modwarn.Warning) (bool, error) {
	for _, item := range affectedItems {
		switch item.Type {
		case modnotif.LookupTypeLocationId:
			if item.Lookup != warning.LocationId.String() {
				return false, nil
			}
		case modnotif.LookupTypeAssetName:
			asset, err := s.getAssetForWarning(warning)
			if err != nil {
				return false, err
			}
			if asset == nil || item.Lookup != asset.Name {
				return false, nil
			}
		case modnotif.LookupTypeAssetId:
			asset, err := s.getAssetForWarning(warning)
			if err != nil {
				return false, err
			}
			if asset == nil || item.Lookup != asset.Identity.String() {
				return false, nil
			}
		default:
			return false, fmt.Errorf("unsupported lookup type: %s", item.Type)
		}
	}

	return true, nil
}

func (s *warningService) getAssetForWarning(warning modwarn.Warning) (*modinv.Asset, error) {
	assetId, ok := warning.GetAssetId()
	if !ok || assetId == nil {
		return nil, nil
	}

	return s.repo.RetrieveAsset(*assetId)
}

func (s *warningService) matchRiskToPriority(risk float64, priority modnotif.NotificationRulePriority) bool {
	switch priority {
	case modnotif.NotificationRulePriorityInfo:
		return risk < 1
	case modnotif.NotificationRulePriorityNotice:
		return risk >= 1 && risk <= 5
	case modnotif.NotificationRulePriorityCaution:
		return risk > 5 && risk <= 20
	case modnotif.NotificationRulePriorityWarning:
		return risk > 20 && risk <= 50
	}

	return false
}
