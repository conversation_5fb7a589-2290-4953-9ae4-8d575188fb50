package warning

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestHistoryWarningService_FindNotificationRuleMatch(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	user1 := modusr.MockUser1(t)
	user2 := modusr.MockUser2(t)
	users := []modusr.User{*user1, *user2}

	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	matchingRule := modnotif.MockNotificationRule1(t)
	matchingRule.Source = "source1"
	matchingRule.EventType = "event_type1"
	matchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*matchingRule}

	notificationQueue := modnotif.MockNotificationQueue1(t)
	notificationQueue2 := modnotif.MockNotificationQueue1(t)
	notificationQueue2.UserId = user2.Identity

	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user2.Identity).Return(notificationSetting, nil)
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)
	mockRepo.EXPECT().CreateNotificationQueue(*notificationQueue).Return(notificationQueue, nil)
	mockRepo.EXPECT().CreateNotificationQueue(*notificationQueue2).Return(notificationQueue2, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	err := service.PopulateNotificationsQueue(*warning)

	// then
	require.NoError(t, err)
}

func TestHistoryWarningService_FindNotificationRuleMatch_NoMatch(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	nonMatchingRule := modnotif.MockNotificationRule2(t)
	nonMatchingRule.Source = "different_source"
	nonMatchingRule.EventType = "event_type1"
	nonMatchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*nonMatchingRule}

	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	err := service.PopulateNotificationsQueue(*warning)

	// then
	require.NoError(t, err)
}

func TestHistoryWarningService_FindNotificationRuleMatch_NoAssetId(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	warning.Code = cjson.JSONB{"someOtherField": "value"} // No assetId
	warning.Source = "source1"
	warning.EventType = "event_type1"

	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	rule := modnotif.MockNotificationRule1(t)
	rule.Source = "source1"
	rule.EventType = "event_type1"
	rule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: "some-lookup",
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*rule}

	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	err := service.PopulateNotificationsQueue(*warning)

	// then
	require.NoError(t, err)
}

func TestHistoryWarningService_FindNotificationRuleMatch_NoNotificationRules(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	notificationSetting := modnotif.MockNotificationSetting3(t) // This mock has empty rules

	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	err := service.PopulateNotificationsQueue(*warning)

	// then
	require.NoError(t, err)
}

func TestHistoryWarningService_matchNotificationRule(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	user := modusr.MockUser1(t)

	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	matchingRule := modnotif.MockNotificationRule1(t)
	matchingRule.Source = "source1"
	matchingRule.EventType = "event_type1"
	matchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*matchingRule}

	mockRepo.EXPECT().GetNotificationSettingByUserId(user.Identity).Return(notificationSetting, nil)
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	result, err := service.matchNotificationRule(*warning, *user)

	// then
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, matchingRule.Identity, result.Identity)
}

func TestHistoryWarningService_matchAffectedItems(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()

	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	result, err := service.matchAffectedItems(affectedItems, *warning)

	// then
	assert.True(t, result)
	assert.NoError(t, err)
}

func TestHistoryWarningService_matchAffectedItems_unknownTpe(t *testing.T) {
	// given
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()

	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   "foo",
		},
	}

	service := &warningService{
		cfg:  *mockConfig,
		repo: mockRepo,
	}

	// when
	result, err := service.matchAffectedItems(affectedItems, *warning)

	// then
	assert.False(t, result)
	assert.Error(t, err)
}
