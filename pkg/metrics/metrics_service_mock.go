// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package metrics

import mock "github.com/stretchr/testify/mock"

// MockMetricsService is an autogenerated mock type for the MetricsService type
type MockMetricsService struct {
	mock.Mock
}

type MockMetricsService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMetricsService) EXPECT() *MockMetricsService_Expecter {
	return &MockMetricsService_Expecter{mock: &_m.Mock}
}

// SomethingHappened provides a mock function with no fields
func (_m *MockMetricsService) SomethingHappened() {
	_m.Called()
}

// MockMetricsService_SomethingHappened_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SomethingHappened'
type MockMetricsService_SomethingHappened_Call struct {
	*mock.Call
}

// SomethingHappened is a helper method to define mock.On call
func (_e *MockMetricsService_Expecter) SomethingHappened() *MockMetricsService_SomethingHappened_Call {
	return &MockMetricsService_SomethingHappened_Call{Call: _e.mock.On("SomethingHappened")}
}

func (_c *MockMetricsService_SomethingHappened_Call) Run(run func()) *MockMetricsService_SomethingHappened_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockMetricsService_SomethingHappened_Call) Return() *MockMetricsService_SomethingHappened_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockMetricsService_SomethingHappened_Call) RunAndReturn(run func()) *MockMetricsService_SomethingHappened_Call {
	_c.Run(run)
	return _c
}

// NewMockMetricsService creates a new instance of MockMetricsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMetricsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMetricsService {
	mock := &MockMetricsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
